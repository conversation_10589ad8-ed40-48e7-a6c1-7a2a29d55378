const path = require('path');
const { NodeSSH } = require('node-ssh');
const { configProdEnv, configTestEnv, configPreEnv } = require('./serviceCnf.js');

const ssh = new NodeSSH();

const envConfigs = {
  prod: configProdEnv,
  test: configTestEnv,
  pre: configPreEnv
};

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${year}${month}${day}${hour}${minute}${second}`;
};

const connectServer = async (env) => {
  const configs = envConfigs[env];
  try {
    await ssh.connect({
      host: configs.host,
      username: configs.username,
      password: configs.password,
      port: configs.port
    });
    console.log(`${env} 服务器连接成功:`, configs.host);
  } catch (err) {
    console.log(`${env} 服务器连接失败:`, err);
    process.exit(1);
  }
};

const uploadFile = async (env) => {
  console.log('开始部署');
  const configs = envConfigs[env];
  try {
    await connectServer(env);
    console.log(`${env} 环境部署中...`);
    const timestamp = await backupDist(env);
    await ssh.putDirectory(configs.distPath, configs.serverPath);
    console.log(`${env} 环境部署成功`);
    console.log(`${env} 环境部署版本: V.${formatTimestamp(timestamp)}`);
  } catch (err) {
    console.log(`${env} 环境部署失败:`, err);
    process.exit(1);
  } finally {
    ssh.dispose();
  }
};

const backupDist = async (env) => {
  const timestamp = new Date().getTime();
  const backupDir = path.join(envConfigs[env].backupPath, formatTimestamp(timestamp));
  console.log(`备份服务器 dist 目录到 ${backupDir}`);
  try {
    await ssh.execCommand(`cp -r ${envConfigs[env].serverPath} ${backupDir}`);
    console.log('备份成功');
    const files = await ssh.execCommand(`ls ${envConfigs[env].backupPath}`);
    const backups = files.stdout.trim().split(/\s+/).map(Number);
    if (backups.length > 5) {
      backups.sort((a, b) => b - a);
      const toDelete = backups.slice(5);
      console.log(`一共要删除 ${toDelete.length} 个旧备份！`);
      for (const backup of toDelete) {
        await ssh.execCommand(`rm -rf ${path.join(envConfigs[env].backupPath, String(backup))}`);
        console.log(`删除 ${String(backup)} 旧备份成功！`);
      }
    }
  } catch (err) {
    console.log('备份失败:', err);
    process.exit(1);
  }
  return timestamp;
};

const rollback = async (env) => {
  const configs = envConfigs[env];
  try {
    await connectServer(env);
    console.log(`${env} 环境开始回滚...`);
    const files = await ssh.execCommand(`ls ${configs.backupPath}`);
    const backups = files.stdout.trim().split(/\s+/).map(Number);
    if (backups.length === 0) {
      console.log(`${env} 环境没有备份可供回滚`);
      return;
    }
    const latestBackup = backups[backups.length - 1];
    console.log(`${env} 环境回滚到版本为 V.${latestBackup} 的备份`);
    await ssh.execCommand(`rm -rf ${configs.serverPath}`);
    await ssh.execCommand(`cp -r ${path.join(configs.backupPath, String(latestBackup))} ${configs.serverPath}`);
    console.log(`${env} 环境版本回滚成功`);
  } catch (err) {
    console.log(`${env} 环境版本回滚失败:`, err);
    process.exit(1);
  } finally {
    ssh.dispose();
  }
};

const main = async () => {
  const [command, env] = process.argv.slice(2);
  switch (command) {
    case 'upload':
      await uploadFile(env);
      break;
    case 'rollback':
      await rollback(env);
      break;
    default:
      console.log('无效的命令');
      process.exit(0);
  }
};

main();
