import router from "./router";
import { ElMessage } from "element-plus";
import NProgress, { remove } from "nprogress";
import "nprogress/nprogress.css";
import { getToken, removeToken } from "@/utils/auth";
import { isHttp } from "@/utils/validate";
import { isRelogin } from "@/utils/request";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
NProgress.configure({ showSpinner: false });

const whiteList = [
  // "/portal/index",
  // "/portal/electronic",
  // "/portal/electronic/list",
  // "/portal/paper",
  // "/portal/paper/record",
  // "/portal/question",
  // "/portal/search",
  // "/portal/news",
  // "/portal/notice",
  // "/portal/topic/electronic",
  // "/portal/topic/paper",
  // "/portal/topic/paper",
  // "/portal/user/collect",
  // "/portal/user/borrow",
  // "/portal/imageOrVideo",
  "/login",
  "/register",
];

router.beforeEach((to, from, next) => {
  NProgress.start();

  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title);
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        useUserStore()
          .getInfo()
          .then(() => {
            isRelogin.show = false;
            usePermissionStore()
              .generateRoutes()
              .then((accessRoutes) => {
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
              });
          })
          .catch((err) => {
            removeToken();

            const loginUrl = `${
              import.meta.env.VITE_APP_SSO_LOGIN_URL
            }?from=${encodeURIComponent(
              import.meta.env.VITE_APP_BASE_FULL_URL
            )}`;
            if (import.meta.env.MODE == "production") {
              window.open(loginUrl, "_self");
            } else {
              // router.push("/login");
              next({ path: "/login" });
            }

            // useUserStore()
            //   .logOut()
            //   .then(() => {
            //     removeToken()
            //     ElMessage.error(err);

            //     next({ path: "/login" });
            //   });
          });
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1 || to.path.includes("/portal/")) {
      // 在免登录白名单，直接进入
      next();
    } else {
      const loginUrl = `${
        import.meta.env.VITE_APP_SSO_LOGIN_URL
      }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
      if (import.meta.env.MODE == "production") {
        window.open(loginUrl, "_self");
      } else {
        // router.push("/login");
        next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      }

      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
