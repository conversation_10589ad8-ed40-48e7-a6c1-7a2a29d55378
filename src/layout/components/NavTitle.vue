<template>
  <div class="app-title">
    <div>{{ title }}</div>
    <div class="right-menu">
      <div class="v-m-x-6">
        <el-icon class="v-pointer home-icon" @click="handleTo" :size="26"
          ><HomeFilled
        /></el-icon>
      </div>

      <template v-if="appStore.device !== 'mobile'">
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
      </template>

      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <!-- <img :src="userStore.avatar" class="user-avatar" /> -->
            <div class="nick-name">
              <div>欢迎您！{{ userStore.nickName }}</div>
              <el-icon><caret-bottom /></el-icon>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import Screenfull from "@/components/Screenfull";
import usePermissionStore from "@/store/modules/permission";
const permissionStore = usePermissionStore();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();

const title = ref(import.meta.env.VITE_APP_TITLE);

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = import.meta.env.VITE_APP_CONTEXT_PATH + "index";
      });
    })
    .catch(() => {});
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}

function handleTo() {
  router.push("/");
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-title {
  width: 100%;
  height: $base-title-height;
  line-height: $base-title-height;
  background-color: $base-menu-background;
  font-size: 20px;
  font-weight: 700;
  padding-left: 25px;
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }
        .nick-name {
          min-width: 98px;
          height: 40px;
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #fff;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 16px;
          font-size: 12px;
        }
      }
    }
  }
}
.home-icon {
  transform: translateY(3px);
}
</style>
