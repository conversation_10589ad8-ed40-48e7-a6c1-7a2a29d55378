<template>
  <div class="navbar-container">
    <el-menu
      v-if="false"
      :default-active="activeMenu"
      class="navbar-menu"
      mode="horizontal"
    >
      <el-menu-item
        v-for="item in filteredList"
        :index="item.component"
        @click="handleRouter(item.component)"
      >
        <!-- <el-icon><icon-menu /></el-icon> -->
        {{ item?.meta?.title || "" }}</el-menu-item
      >
      <!-- <el-menu-item  index="/portal/index" @click="handleRouter('/portal/index')">首页</el-menu-item>
      <el-menu-item index="/portal/electronic" @click="handleRouter('/portal/electronic')">电子资料馆</el-menu-item>
      <el-menu-item index="/portal/paper" @click="handleRouter('/portal/paper')">实体资料馆</el-menu-item> -->
      <!-- <el-menu-item
        index="/portal/question"
        @click="handleRouter('/portal/question')"
        >常见问题</el-menu-item
      > -->
    </el-menu>

    <div class="navbar-menu v-flex">
      <div v-for="(item, index) in filteredList" :key="item.path">
        <div class="v-flex">
          <div
            class="navbar-menu-item"
            style="height: 80px"
            :class="{ 'active-menu': route.path == item.component }"
            @click="handleRouter(item.component, item, route)"
          >
            <span
              :style="{
                background: `url(${getImageUrl(
                  index
                )}) center / 100% 100% no-repeat`,
              }"
              class="nav-icon"
            ></span>
            {{ item?.meta?.title || "" }}
          </div>
          <div
            v-if="filteredList.length - 1 !== index"
            class="divider-line"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import iconMenu0 from "@/assets/images/icon-menu0.png";
import iconMenu1 from "@/assets/images/icon-menu1.png";
import iconMenu2 from "@/assets/images/icon-menu2.png";

const route = useRoute();
const router = useRouter();
const defalutPaths = [
  {
    hidden: false,
    component: "/portal/index",
    meta: {
      title: "首页",
    },
  },
  {
    hidden: false,
    component: "/portal/electronic",
    meta: {
      title: "电子资料馆",
    },
  },
  {
    hidden: false,
    component: "/portal/paper",
    meta: {
      title: "实体资料馆",
    },
  },
  // {
  //   "hidden": false,
  //   "component": "/portal/notice",
  //   "meta": {
  //     "title": "资讯信息",
  //   }
  // },
  // {
  //   "hidden": false,
  //   "component": "/portal/notice/details",
  //   "meta": {
  //     "title": "资讯详情",
  //   }
  // }
  {
    hidden: true,
    component: "/portal/imageOrVideo",
    meta: {
      title: "图文影音馆",
    },
  },
];
const props = defineProps({
  paths: {
    type: Array,
    default: [],
  },
});

const filteredList = computed(() => {
  const paths = props.paths.length > 0 ? props.paths : defalutPaths;
  return paths.filter((item) => !item.hidden && item.meta?.title);
});

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
const handleRouter = (path, a, b) => {
  console.log(a, b, a.component == b.path);
  if (activeMenu != path) {
    router.push(path);
  }
};

const getImageUrl = (index) => {
  const icons = [iconMenu0, iconMenu1, iconMenu2]; // 图标数组
  // 根据索引生成图像 URL
  return icons[index] || iconMenu0;
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.navbar-menu {
  --el-menu-text-color: #fff;
  --el-menu-active-color: #285579;
  --el-menu-item-font-size: 18px;
  --el-menu-bg-color: transparent;
  --el-menu-border-color: transparent;
  --el-menu-hover-bg-color: transparent;
  --el-menu-base-level-padding: 50px;
  --el-menu-horizontal-height: 80px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  margin: 0 auto;

  .navbar-menu-item {
    cursor: pointer;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    line-height: 26px;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 38px;
    line-height: 80px;

    .nav-icon {
      display: inline-block;
      width: 20px;
      height: 20px;
      position: relative;
      top: 4px;
      margin-right: 10px;
      // background: url("../../../assets/images/icon-menu0.png") center / 100%
      //   100% no-repeat;
    }
  }
}

.navbar-container {
  background: #114f87;
  border-radius: 0px 0px 0px 0px;
  height: 80px;
}

@media screen and (min-width: 1440px) {
  .navbar-menu {
    width: $base-page-container-width;
  }
}

@media screen and (min-width: 1920px) {
  .navbar-menu {
    width: $base-page-container-width;
  }
}

:deep(.el-menu--horizontal > .el-menu-item.is-active) {
  color: #fff !important;
  border-bottom: 4px solid #fff;
  border-radius: 2px;
}

// :deep(.el-menu) {
//   background-color: transparent;
// }

// :deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):focus, ) {
//   background-color: transparent;
//   color: #fff !important;
//   font-weight: 500;
// }

// :deep(.el-menu--horizontal > .el-menu-item.is-active) {
//   // color: #fff !important;
//   border-color: transparent;
// }
:deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):hover) {
  color: #fff;
  background-color: #409eff;
}

// :deep(.el-menu) {
//   width: 55%;
//   justify-content: space-around;
// }

.divider-line {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.49) 53%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 0px 0px 0px 0px;
  width: 1px;
  height: 46px;
}

.active-menu {
  border-bottom: 4px solid #fff;
  position: relative;
  &:after {
    content: " ";
    display: inline-block;
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);

    width: 9px;
    height: 6px;
    background: #fff;
  }
}
</style>
