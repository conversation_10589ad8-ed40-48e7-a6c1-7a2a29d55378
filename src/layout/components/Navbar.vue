<template>
  <div class="navbar">
    <div class="navbar-top">
      <div class="navbar-top-l">
        <!-- <img
          src="@/assets/images/tongji_logo_new.png"
          alt=""
          style="width: 66px; margin-right: 8px"
        /> -->
        <div class="title-box">
          <div class="title">{{ title }}</div>
          <div class="sub">Bureau Statistics Library Of Henan</div>
        </div>
      </div>
      <div class="navbar-top-r">
        <div class="v-flex">
          <div
            v-if="userInfo.token"
            v-hasRole="['admin', 'library_admin']"
            class="v-m-x-10 nav-item"
            @click="routerGo(routerPath.Admin)"
          >
            <span class="nav-icon1 nav-icon"></span>
            后台
          </div>
          <!-- <div v-if="userInfo.token" class="v-m-x-10 nav-item">捐赠</div> -->
          <!-- <div v-if="userInfo.token" class="v-m-x-10 nav-item">留言</div> -->
          <div
            v-if="!userInfo.token"
            class="v-m-x-10 nav-item"
            @click="handelLogin"
          >
            <span class="nav-icon2 nav-icon"></span>
            登录
          </div>
          <el-dropdown
            v-if="userInfo.token"
            @command="handleCommand"
            class="right-menu-item hover-effect v-m-x-10"
            trigger="click"
            popper-class="my-menu"
          >
            <div class="avatar-wrapper">
              <div class="nick-name nav-item">
                <span class="nav-icon2 nav-icon"></span>
                我的
                <el-icon><caret-bottom /></el-icon>
              </div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="borrow">
                  <span>我的借书</span>
                </el-dropdown-item>
                <el-dropdown-item command="collect">
                  <span>我的收藏</span>
                </el-dropdown-item>
                <el-dropdown-item command="picture">
                  <span>我的图文</span>
                </el-dropdown-item>
                <el-dropdown-item command="video">
                  <span>我的影音</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <TopNav :paths="routerPath.Portal" />
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import useUserStore from "@/store/modules/user";
import defaultSettings from "@/settings";
import TopNav from "../components/TopNav/index";
import { getRouters } from "@/api/menu.js";
import usePermissionStore from "@/store/modules/permission";
const router = useRouter();
const userStore = useUserStore();
const title = computed(() => defaultSettings.title);
const userInfo = computed(() => useUserStore());
const routesData = computed(() => usePermissionStore().routeDataSource);
const routerPath = reactive({ Portal: [], Admin: "" });
const routerGo = (url, query) => {
  console.log(url, query);
  router.push({ path: url, query: query || {} });
};

/**
 * 获取路由数据
 * pinia 里存的有这些数据，不用每次都拉，另外，门户不登录的情况是可以看的，从接口里面拉会造成进入门户就提示让登陆的问题！
 */

function fetchRouters() {
  const routes = routesData.value;

  routerPath.Portal =
    routes.find((item) => item.name == "Portal")?.children || null;
  const adminPath =
    routes.find((item) => item.name == "Admin")?.children || null;
  routerPath.Admin = adminPath?.children?.[0].component || "/admin/home";
}
function handleCommand(command) {
  switch (command) {
    case "borrow":
      routerGo("/portal/user/borrow");
      break;
    case "collect":
      routerGo("/portal/user/collect");
      break;
    case "picture":
      routerGo("/portal/user/picture");
      break;
    case "video":
      routerGo("/portal/user/video");
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  console.log(userInfo, "userInfo");
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        handelLogin();
      });
    })
    .catch(() => {});
}

const handelLogin = () => {
  const loginUrl = `${
    import.meta.env.VITE_APP_SSO_LOGIN_URL
  }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
  if (import.meta.env.MODE == "production") {
    // window.open(loginUrl, "_self");

    router.push("/");
  } else {
    router.push("/");
  }
};

const emits = defineEmits(["setLayout"]);
onMounted(async () => {
  routesData && routesData.value.length && fetchRouters();
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.navbar {
  // height: 150px;
  // width: 100%;
  // height: 445px;
  overflow: hidden;
  position: relative;
  // background:
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background: url("../../assets/images/top_bg.png") center top / 100% 365px
    no-repeat;

  .nav-header {
    width: $base-page-container-width;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    // background: url("../assets/images/top_bg.png");
    // ;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .title-container {
    font-size: 38px;
    color: #fff;
    font-weight: 600;
    display: inline-block;

    img {
      width: 60px;
      height: 60px;
      position: relative;
      left: 20px;
      top: 15px;
      margin-right: 20px;
    }
  }

  .navbar-top {
    // background: url("../../assets/images/top_bg.png") no-repeat;

    height: 365px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    // padding: 0 70px;
    // padding-top: 10px;
    width: $base-page-container-width;
    margin: 0 auto;
  }

  .navbar-top-l {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: calc($base-page-container-width - 250px);
    // --el-menu-bg-color
    .title-box {
      width: $base-page-container-width;
      display: flex;
      flex-direction: column;

      .title {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 64px;
        color: #114f87;
        line-height: 93px;
        letter-spacing: 6px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .sub {
        font-family: Arial, Arial;
        font-weight: 400;
        font-size: 32px;
        color: #114f87;
        line-height: 41px;
        letter-spacing: 2px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .navbar-top-r {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    --el-menu-text-color: #fff;
    --el-menu-active-color: #285579;
    --el-menu-item-font-size: 18px;
    --el-menu-bg-color: transparent;
    --el-menu-border-color: transparent;
    --el-menu-hover-bg-color: transparent;
    // position: relative;
    // top: 20px;
  }
  .el-sub-menu > .el-sub-menu__title {
  }
  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    // height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #fff;
      vertical-align: text-bottom;
      top: -1px;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
  }
}

.avatar-container {
  margin-right: 40px;

  .avatar-wrapper {
    margin-top: 5px;
    position: relative;

    .nick-name {
      min-width: 98px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #fff;
    }

    .user-avatar {
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 10px;
    }

    i {
      cursor: pointer;
      position: absolute;
      right: -20px;
      top: 25px;
      font-size: 12px;
    }
  }
}

.nav-item {
  font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
  font-weight: 500;
  font-size: 18px;
  color: #114f87;
  line-height: 25px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;

  .nav-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    position: relative;
    left: 10px;
    top: 3px;
  }

  .nav-icon1 {
    background: url("../../assets/images/icon-admin.png") center / 100% 100%
      no-repeat;
  }

  .nav-icon2 {
    background: url("../../assets/images/icon-user.png") center / 100% 100%
      no-repeat;
  }
}
</style>
