<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <div
      style="margin-left: 0"
      :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      class="main-container"
    >
      <div class="fixed-header1 fixed-header-front">
        <navbar @setLayout="setLayout" />
      </div>
      <app-main />
      <settings ref="settingRef" />
      <!--  底部  -->
      <div class="el-login-footer">
        <div class="v-flex v-row-center">
          <div>
            <span class="v-m-x-10">{{ title }}</span>
            <span class="v-m-x-10">邮编：450000</span>
          </div>
          <div class="v-m-y-10 v-m-x-10">
            服务咨询电话：0371-6666599 400-666-6666
          </div>
          <div class="v-m-y-10">
            地址：河南省郑州市金水东路39号河南省人民政府E区8楼
          </div>
        </div>
        <div>Copyright © 河南统计资料管理应用中心 版权所有</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { useWindowSize } from "@vueuse/core";
import { AppMain, Navbar, Settings, TagsView } from "./components";
import defaultSettings from "@/settings";

import useAppStore from "@/store/modules/app";

const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);
const title = computed(() => defaultSettings.title);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === "mobile",
}));

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === "mobile" && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice("mobile");
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice("desktop");
  }
});

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  // background: url("@/assets/images/bg_1.png");
  background-size: cover;
  background-position: center;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: 100%;
  transition: width 0.28s;
  background: #2e90df;
}

.hideSidebar .fixed-header {
  // width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.el-login-footer {
  background: #114f87;
  padding: 40px;
  // position: fixed;
  // bottom: 0;
  width: 100%;
  height: 148px;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 1px;
  // z-index: 99;
}
</style>
