


import request from '@/utils/request'

// 专题资料 分页列表
export function topicQuery(query) {
  return request({
    url: '/subject/material/query/page',
    method: 'post',
    data: query
  })
}

// 专题资料 资料类目-列表
export function topicCategoryQuery(query) {
    return request({
      url: '/subject/category/query/list',
      method: 'post',
      data: query
    })
  }
  // 专题轮播界面
export function topicCarousel(query) {
  // console.log("topicCarousel")
  return request({
    url: '/system/roll/query',
    method: 'post',
    data: query
  })
}
