import request from '@/utils/request'

// 首页简介
export function electronicIntro() {
  return request({
    url: '/home/<USER>/intro',
    method: 'get',
    params: {}
  })
}
// 热门排行
export function electronicHotRank(query) {
  return request({
    url: '/home/<USER>/hot-rank',
    method: 'post',
    data: query
  })
}

// 资料概览设置查询
export function electronicSettingQuery() {
  return request({
    url: '/home/<USER>/setting/query',
    method: 'post',
    params: {}
  })
}

// 资料概览设置查询
export function electronicSetting() {
  return request({
    url: '/home/<USER>/setting/query',
    method: 'get',
    params: {}
  })
}


// 资料概览设置查询
export function electronic() {
  return request({
    url: '/home/<USER>/setting/query',
    method: 'get',
    params: {}
  })
}
// 订阅
export function subscription(id) {
  return request({
    url: '/subscription/material/sub/'+id,
    method: 'post'
  })
}
