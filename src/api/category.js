import request from "@/utils/request";

// 查询轮播图
export function listTreeCategory(data) {
  // return request.post( '/category/tree',data)
  return request({
    url: "/category/query/tree",
    method: "post",
    data: data,
  });
}

/**
 * 资料类目-列表
 * http://192.168.118.4:40001/project/163/interface/api/13933
 * @param {*} data
 * @returns
 */
export function QueryListCategory(data) {
  return request({
    url: "/category/query/list",
    method: "post",
    data: data,
  });
}
