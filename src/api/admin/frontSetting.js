import request from "@/utils/request";

// 电子馆首页展示设置-保存
export function homeElectronicSave(data) {
  return request({
    url: "/home-show/electronic-save",
    method: "post",
    data,
  });
}

// 电子馆首页展示设置-查询
export function homeElectronicSelect() {
  return request({
    url: "/home-show/electronic-select",
    method: "get",
  });
}

// 实体馆首页展示设置-保存
export function homePhysicalSave(data) {
  return request({
    url: "/home-show/physical-save",
    method: "post",
    data,
  });
}

// 实体馆首页展示设置-查询
export function homePhysicalSelect() {
  return request({
    url: "/home-show/physical-select",
    method: "get",
  });
}