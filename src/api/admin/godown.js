import request from "@/utils/request";
// 出入库列表
export function getRecordList(data) {
  return request({
    url: "/physical/stock/record/detail/page",
    method: "post",
    data,
  });
}
//库存概览
export function getOverviewList(data) {
  return request({
    url: "/warehouse/query/shelf-page",
    method: "post",
    data,
  });
}
// 当前存放（书目管理-查看）
export function getStockPageList(data) {
  return request({
    url: "/material/physical/stock-page",
    method: "post",
    data,
  });
}
// 派送出库记录导出
export function exportStockDispatch(data) {
  return request({
    url: `/physical/stock/dispatch/list-export`,
    method: "post",
    responseType: "blob",
    data,
  });
}

// 库存出入库记录导出
export function exportStockInOut(data) {
  return request({
    url: `/physical/stock/in-out/list-export`,
    method: "post",
    responseType: "blob",
    data,
  });
}
