import request from "@/utils/request";

//  专题类目管理 专题类目查询列表
export function getMonographsList(data) {
  return request({
    url: "/subject/category/query/list",
    method: "post",
    data,
  });
}

//  专题类目管理 新增专题类目
export function addMonographsList(data) {
  return request({
    url: "/subject/category/add",
    method: "post",
    data,
  });
}

//  专题类目管理 修改专题类目
export function updateMonographsList(data) {
  return request({
    url: "/subject/category/edit",
    method: "post",
    data,
  });
}

//  专题类目管理 删除专题类目
export function deleteMonographsList(id) {
  return request({
    url: `/subject/category/del/${id}`,
    method: "post",
  });
}

//  专题资料管理  资料查询
export function getMonographsBookList(data) {
  return request({
    url: "/subject/material/query/page",
    method: "post",
    data,
  });
}
//  专题资料管理  资料新增
export function addMonographsBook(data) {
  return request({
    url: "/subject/material/add",
    method: "post",
    data,
  });
}

//  专题资料管理  资料修改
export function updateMonographsBook(data) {
  return request({
    url: "/subject/material/edit",
    method: "post",
    data,
  });
}

//  专题资料管理  资料删除
export function deleteMonographsBook(id) {
  return request({
    url: `/subject/material/del/${id}`,
    method: "post",
  });
}
