import request from "@/utils/request";

//  图文资料馆 图文管理 图文列表
export function getVideoList(data) {
  return request({
    url: "/material/video/page",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文列表前台列表
export function getVideoListFront(data) {
  return request({
    url: "/home/<USER>/my-video",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文新增
export function addVideo(data) {
  return request({
    url: "/material/video/add",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文编辑
export function editVideo(data) {
  return request({
    url: "/material/video/edit",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文编辑
export function deleteVideo(id) {
  return request({
    url: `/material/video/del/${id}`,
    method: "post",
  });
}
