import request from "@/utils/request";

// 资料类目设置 资料概览设置查询
export function getDataViewer(data) {
  return request({
    url: "/home/<USER>/setting/query",
    method: "get",
    data,
  });
}

// 资料类目设置 资料概览设置修改
export function setDataViewer(data) {
  return request({
    url: "/home/<USER>/setting/edit",
    method: "post",
    data,
  });
}

//  资料类目设置  资料类目查询
export function getCategoryTree(data) {
  return request({
    url: "/category/query/tree",
    method: "post",
    data,
  });
}

//  资料类目设置  资料类目新增
export function addCategoryNode(data) {
  return request({
    url: "/category/add",
    method: "post",
    data,
  });
}
//  资料类目设置  资料类目修改
export function updateCategoryNode(data) {
  return request({
    url: "/category/edit",
    method: "post",
    data,
  });
}

//  资料类目设置  资料类目删除
export function deleteCategoryNode(id) {
  return request({
    url: `/category/del/${id}`,
    method: "post",
  });
}

//  资料类目设置  资料类目详情
export function getCategoryNode(id) {
  return request({
    url: `/category/query/by-id/${id}`,
    method: "get",
  });
}

//  资料类目管理  资料查询
export function getCategoryBookList(data) {
  return request({
    url: "/material/page",
    method: "post",
    data,
  });
}
//  资料管理-列表
export function getElectronicBookList(data) {
  return request({
    url: "/material/electronic/page",
    method: "post",
    data,
  });
}
// 资料管理-列表分页
export function getPhysicalPageList(data) {
  return request({
    url: "/material/physical/page",
    method: "post",
    data,
  });
}
//  资料类目管理  资料查询 前台接口
export function getCategoryBookListFront(data) {
  return request({
    url: "/home/<USER>/my-collection",
    method: "post",
    data,
  });
}
//  资料类目管理  资料新增
export function addCategoryBook(data) {
  return request({
    url: "/material/add",
    method: "post",
    data,
  });
}

//  资料类目管理  资料修改
export function updateCategoryBook(data) {
  return request({
    url: "/material/edit",
    method: "post",
    data,
  });
}

//  资料类目管理  资料删除
export function deleteCategoryBook(id) {
  return request({
    url: `/material/del/${id}`,
    method: "post",
  });
}

//  资料类目管理  资料删除
export function getBookClusterList() {
  return request({
    url: `/system/dict/data/type/electronic_cluster_type`,
    method: "get",
  });
}

//  实体资料管理-导出
export function exportMaterialPhysical(data) {
  return request({
    url: `/material/physical/list-export`,
    method: "post",
    responseType: "blob",
    data,
  });
}
// 电子资料管理-导出
export function exportMaterialElectronic(data) {
  return request({
    url: `/material/electronic/list-export`,
    method: "post",
    responseType: "blob",
    data,
  });
}

//  电子资料统计列表
export function statisticSelectronic(data) {
  return request({
    url: "/material/electronic/summary-page",
    method: "post",
    data,
  });
}

export function categorySort(data) {
  return request({
    url: "/category/sort",
    method: "post",
    data,
  });
}
