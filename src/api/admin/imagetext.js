import request from "@/utils/request";

//  图文资料馆 图文管理 图文列表
export function getImageTextList(data) {
  return request({
    url: "/material/pic/page",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文列表前台页面
export function getImageTextListFront(data) {
  return request({
    url: "/home/<USER>/my-pic",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文新增
export function addImageText(data) {
  return request({
    url: "/material/pic/add",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文编辑
export function editImageText(data) {
  return request({
    url: "/material/pic/edit",
    method: "post",
    data,
  });
}

//  图文资料馆 图文管理 图文编辑
export function deleteImageText(id) {
  return request({
    url: `/material/pic/del/${id}`,
    method: "post",
  });
}

//  图文资料馆 图文管理 图文详情
export function getImageText(id) {
  return request({
    url: `/material/pic/by-id/${id}`,
    method: "post",
  });
}
