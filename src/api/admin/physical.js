import request from "@/utils/request";

// 保存仓库
export function wareSave(data) {
  return request({
    url: "/warehouse/save",
    method: "post",
    data,
  });
}

// 删除仓库
export function wareDelete(id) {
  return request({
    url: `/warehouse/del/${id}`,
    method: "post",
  });
}

// 仓库列表（所有-不分页）
export function wareList(data) {
  return request({
    url: "/warehouse/query/warehouse-list",
    method: "post",
    data,
  });
}

// 仓库分页
export function warePageList(data) {
  return request({
    url: "/warehouse/query/warehouse-page",
    method: "post",
    data,
  });
}

// 保存书架
export function bookSave(data) {
  return request({
    url: "/warehouse/shelf/save",
    method: "post",
    data,
  });
}

// 删除书架
export function bookDelete(id) {
  return request({
    url: `/warehouse/shelf/del/${id}`,
    method: "post",
  });
}

// 书架列表（所有-不分页）
export function bookList(data) {
  return request({
    url: "/warehouse/query/shelf-list",
    method: "post",
    data,
  });
}

// 书架列表
export function bookPageList(data) {
  return request({
    url: "/warehouse/query/shelf-page",
    method: "post",
    data,
  });
}

// 资料首页
export function materialSet() {
  return request({
    url: "/home/<USER>/setting/query",
    method: "get",
  });
}

// 资料首页-编辑
export function materialSetEdit(data) {
  return request({
    url: "/home/<USER>/setting/edit",
    method: "post",
    data,
  });
}

// 资料类目-列表-分页
export function categoryPageList(data) {
  return request({
    url: "/category/query/page",
    method: "post",
    data,
  });
}

// 资料类目-明细
export function categoryDetail(id) {
  return request({
    url: `/category/query/by-id/${id}`,
    method: "get",
  });
}

// 资料类目-编辑
export function categoryEdit(data) {
  return request({
    url: "/category/edit",
    method: "post",
    data,
  });
}

// 资料管理-详情
export function materialDetail(id) {
  return request({
    url: `/material/by-id/${id}`,
    method: "post",
  });
}

// 资料入库
export function inStock(data) {
  return request({
    url: "/physical/stock/in-stock",
    method: "post",
    data,
  });
}

// 资料出库
export function outStock(data) {
  return request({
    url: "/physical/stock/out-stock",
    method: "post",
    data,
  });
}

// 根据资料id获取资料库存分布
export function distributionStock(id) {
  return request({
    url: `/physical/stock/by-material/${id}`,
    method: "get",
  });
}

// 出入库列表
export function inOutPage(data) {
  return request({
    url: "/physical/stock/record/page",
    method: "post",
    data,
  });
}

// 根据code获取出入库记录
export function srockRecords(code) {
  return request({
    url: `/physical/stock/record/by-code/${code}`,
    method: "get",
  });
}

// 借阅列表
export function borrowPage(data) {
  return request({
    url: "/physical/borrow/page",
    method: "post",
    data,
  });
}

// 借阅详情
export function borrowDetail(id) {
  return request({
    url: `/physical/borrow/detail/${id}`,
    method: "get",
  });
}

// 借阅-同意
export function borrowAgree(data) {
  return request({
    url: "/physical/borrow/approval/agree",
    method: "post",
    data,
  });
}

// 借阅-归还
export function borrowBack(data) {
  return request({
    url: "/physical/borrow/approval/giveback",
    method: "post",
    data,
  });
}

// 借阅申请-驳回
export function borrowReject(id) {
  return request({
    url: `/physical/borrow/approval/reject/${id}`,
    method: "post",
  });
}

// 借阅-办结同意
export function borrowDoneAgree(data) {
  return request({
    url: "/physical/borrow/approval/abort/agree",
    method: "post",
    data,
  });
}

// 借阅-办结驳回
export function borrowDoneReject(data) {
  return request({
    url: "/physical/borrow/approval/abort/reject",
    method: "post",
    data,
  });
}

// 仓架管理分页
export function warehousePage(data) {
  return request({
    url: "/warehouse/query/manage-page",
    method: "post",
    data,
  });
}
// 书目管理-查看
export function physicalStockPage(data) {
  return request({
    url: "/material/physical/stock-page",
    method: "post",
    data,
  });
}
// 书目管理-当前库存总量
export function physicalStockCount(data) {
  return request({
    url: "/physical/stock/count",
    method: "post",
    data,
  });
}

// 实体书管理-增印
export function reprintPage(data) {
  return request({
    url: "/physical/stock/reprint",
    method: "post",
    data,
  });
}

// 实体书管理-派送
export function deliveryPage(data) {
  return request({
    url: "/physical/stock/dispatch",
    method: "post",
    data,
  });
}

// 新增实体资料
export function queryPhysicalAdd(data) {
  return request({
    url: "/material/physical/add",
    method: "post",
    data,
  });
}
// 编辑实体资料
export function queryPhysicalEdit(data) {
  return request({
    url: "/material/physical/edit",
    method: "post",
    data,
  });
}

//借阅统计
export function queryStatsPage(data) {
  return request({
    url: "/physical/borrow/stats-page",
    method: "post",
    data,
  });
}

// 借阅详情
export function getBorrowDetailById(id) {
  return request({
    url: `/physical/borrow/detail/${id}`,
    method: "get",
  });
}

// 异常记录
export function getBorrowaBnormalRecord(data) {
  return request({
    url: `/physical/borrow/abnormal-record`,
    method: "post",
    data,
  });
}
// 入库记录管理编辑
export function stockRecordInEdit(data) {
  return request({
    url: `/physical/stock/record/in/edit`,
    method: "post",
    data,
  });
}

// 出库记录管理编辑
export function stockRecordOutEdit(data) {
  return request({
    url: `/physical/stock/record/out/edit`,
    method: "post",
    data,
  });
}

// 派送记录管理编辑
export function stockRecordDispatchEdit(data) {
  return request({
    url: `/physical/stock/record/dispatch/edit`,
    method: "post",
    data,
  });
}

// 入库记录管理删除
export function stockRecordInDel(data) {
  return request({
    url: `/physical/stock/record/in/del`,
    method: "post",
    data,
  });
}

// 出库记录管理删除
export function stockRecordOutDel(data) {
  return request({
    url: `/physical/stock/record/out/del`,
    method: "post",
    data,
  });
}

// 派送记录管理删除
export function stockRecordDispatchDel(data) {
  return request({
    url: `/physical/stock/record/dispatch/del`,
    method: "post",
    data,
  });
}

// 仓库列表
export function warehouseStockList(data) {
  return request({
    url: `/material/physical/warehouse-stock-page`,
    method: "post",
    data,
  });
}
// 仓库列表详情
export function warehouseStockDetail(data) {
  return request({
    url: `/warehouse/query/shelf-stock-page`,
    method: "post",
    data,
  });
}
// 仓库资料挪架
export function movewarehouseApi(data) {
  return request({
    url: `/physical/stock/material/move-warehouse`,
    method: "post",
    data,
  });
}

// 书架资料挪架
export function moveShelfApi(data) {
  return request({
    url: `/physical/stock/material/move-shelf`,
    method: "post",
    data,
  });
}

// 仓库管理导出
export function warehouseStockListExport(data) {
  return request({
    url: `/material/physical/warehouse-stock-list-export`,
    method: "post",
    data,
  });
}

// 书架管理导出
export function shelfListExport(data) {
  return request({
    url: `/warehouse/query/shelf-list-export`,
    method: "post",
    responseType: "blob",
    data,
  });
}

// 派送出库导出
export function dispatchListExport(data) {
  return request({
    url: `/physical/stock/dispatch/list-export`,
    method: "post",
    data,
  });
}

// 出库导出
export function outListExport(data) {
  return request({
    url: `/physical/stock/out/list-export`,
    method: "post",
    data,
  });
}

// 入库导出
export function inListExport(data) {
  return request({
    url: `/physical/stock/in/list-export`,
    method: "post",
    data,
  });
}

// 资料总数
export function materialNumStats(data) {
  return request({
    url: `/material/physical/page-stats`,
    method: "post",
    data,
  });
}

export function bookPageListExport(data) {
  return request({
    url: `/warehouse-query/export`,
    method: "post",
    responseType: "blob",
    data,
  });
}

export function warehouseSummary(data) {
  return request({
    url: `/warehouse-query/summary`,
    method: "post",
    data,
  });
}

export function warehouseList(data) {
  return request({
    url: `/warehouse-query/page`,
    method: "post",
    data,
  });
}
