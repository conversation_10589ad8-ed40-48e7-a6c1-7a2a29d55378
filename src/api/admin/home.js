import request from "@/utils/request";

// 更新首页首页配置
export function addHomeCarousel(data) {
  return request({
    url: "/system/roll/edit",
    method: "post",
    data,
  });
}

// 查询首页配置
export function getHomeCarousel(data) {
  return request({
    url: "/system/roll/query",
    method: "post",
    data,
  });
}

//首页统计-电子馆概览
export function getElecLibView() {
  return request({
    url: "/home/<USER>/elec-lib-view",
    method: "get",
  });
}

//首页统计-借阅状态统计
export function getSubsStatusStats() {
  return request({
    url: "/home/<USER>/subs-status-stats",
    method: "get",
  });
}

//首页统计-各处统计
export function getDeptSubsStats() {
  return request({
    url: "/home/<USER>/dept-subs-stats",
    method: "get",
  });
}

//首页统计-资料统计
export function getMaterialStats() {
  return request({
    url: "/home/<USER>/material-stats",
    method: "get",
  });
}

//首页统计-图文馆概览
export function getPicLibView() {
  return request({
    url: "/home/<USER>/pic-lib-view",
    method: "post",
  });
}

//首页统计-影音馆概览
export function getVideoLibView() {
  return request({
    url: "/home/<USER>/video-lib-view",
    method: "post",
  });
}

// 仓库统计
export function getWarehouseView() {
  return request({
    url: "/home/<USER>/warehouse-stats",
    method: "get",
  });
}

//首页统计-借阅累积
export function getCategoryView() {
  return request({
    url: "/home/<USER>/borrow-stats/category",
    method: "get",
  });
}

//首页统计-实体资料馆概览
export function getPhysicalIntro() {
  return request({
    url: "/home/<USER>/physical/intro",
    method: "get",
  });
}
