import request from "@/utils/request";

// 查询OSS对象存储列表
export function listOss(query) {
  return request({
    url: "/system/oss/list",
    method: "get",
    params: query,
  });
}

// 查询OSS对象基于id串
export function listByIds(ossId) {
  return request({
    url: "/system/oss/listByIds/" + ossId,
    method: "get",
  });
}

// 删除OSS对象存储
export function delOss(ossId) {
  return request({
    url: "/system/oss/" + ossId,
    method: "delete",
  });
}
/**
 *
 * @param {*} data fomdata 二进制流
 * @param {*} path 相对路径：file-普通文件; library-资料馆
 * @param {*} useOriginalFilename 是否使用源文件名：ture，false
 * @returns
 */
export const singleUpload = (
  data,
  path = "library",
  useOriginalFilename = true
) => {
  return request({
    method: "POST",
    url: `/oss/upload/single/${path}/${useOriginalFilename}`,
    data,
    headers: {
      repeatSubmit: false,
    },
    timeout: 50000,
  });
};

// 切片获取minio地址
export const multipartinit = (data) => {
  return request({
    method: "POST",
    url: "/oss/multipart/init",
    data,
    timeout: 50000,
  });
};

// 分片
export const spiteFilePart = (uploadId, partNumber, data) => {
  return request({
    method: "POST",
    url: `/oss/multipart/upload/${uploadId}/${partNumber}`,
    data: data,
    headers: {
      repeatSubmit: false,
    },
    timeout: 50000,
  });
};

// 完成上传并合并
export const multipartmerge = (uploadId) => {
  return request({
    method: "get",
    url: "/oss/multipart/merge/" + uploadId,
  });
};
