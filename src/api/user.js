import request from "@/utils/request";

// 借阅 借阅列表
export function getBorrowList(query) {
  return request({
    url: "/physical/borrow/page",
    method: "post",
    data: query,
  });
}
// 借阅 借阅列表前台接口
export function getBorrowListFront(query) {
  return request({
    url: "/home/<USER>/my-borrow",
    method: "post",
    data: query,
  });
}

// 借阅  申请办结
export function pushBorrowCompleted(query) {
  return request({
    url: "/physical/borrow/abort/apply",
    method: "post",
    data: query,
  });
}

// 收藏  取消收藏
export function cancelSubscription(id) {
  return request({
    url: `/subscription/material/un-sub/${id}`,
    method: "post",
  });
}
