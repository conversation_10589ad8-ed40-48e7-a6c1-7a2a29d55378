import request from "@/utils/request";

export function materialPage(query) {
  return request({
    url: "/material/page",
    method: "post",
    data: query,
  });
}
// 资料查询
export function materialPagePortal(query) {
  return request({
    url: "/home/<USER>/stock-page",
    method: "post",
    data: query,
  });
}

//实体资料馆 借阅申请
export function physicalApply(query) {
  return request({
    url: "/physical/borrow/apply",
    method: "post",
    data: query,
  });
}
// 根据ids查询类目名称
export function queryCategoryNameByIds(ids) {
  return request({
    url: `/category/query/name/${ids}`,
    method: "get",
  });
}

// 最新电子资料
export function queryElectLatest(query) {
  return request({
    url: `/home/<USER>/latest`,
    method: "post",
    data: query,
  });
}

// 资料管理-列表分页
export function queryPhysical(query) {
  return request({
    url: `/home/<USER>/page`,
    method: "post",
    data: query,
  });
}

// 增加点击量
export function addIncreaseClick(materialId) {
  return request({
    url: `/stats/pv/increase-click/${materialId}`,
    method: "get",
  });
}

// 首页-电子馆首页展示设置-查询
export function homeElectronicSelect() {
  return request({
    url: `/home-show/electronic-select`,
    method: "get",
  });
}

// 首页-实体馆首页展示设置-查询
export function homePhysicalSelect() {
  return request({
    url: `/home-show/physical-select`,
    method: "get",
  });
}
