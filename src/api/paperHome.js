import request from "@/utils/request";
// 首页设置概览查询
export function paperStting() {
  return request({
    url: "/home/<USER>/setting/query",
    method: "get",
    params: {},
  });
}
export function categoryQuery(query) {
  return request({
    url: "/category/query/page",
    method: "post",
    data: query,
  });
}
// 实体馆概览查询
export function paperCategoryQuery(query) {
  return categoryQuery({
    categoryType: 2,
    ...query,
    // size: 8
  });
}

// 汇总数据
export function queryPhysicalSummary(ids) {
  return request({
    url: `/home/<USER>/summary`,
    method: "get",
  });
}
