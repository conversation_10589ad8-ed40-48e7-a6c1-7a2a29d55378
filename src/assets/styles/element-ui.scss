// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;

  .el-dialog__title {
    font-size: 18px !important;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-button {
  cursor: pointer;
}

/* 全局设置所有Element Plus组件字体大小为 16px */

/* 表格相关 */
.el-table,
.el-table th,
.el-table td,
.el-table__header,
.el-table__body,
.el-table .cell {
  font-size: 16px !important;
}

/* 按钮 */
.el-button,
.el-button span {
  font-size: 16px !important;
}

/* 输入框 */
.el-input,
.el-input__inner,
.el-input__wrapper,
.el-textarea,
.el-textarea__inner {
  font-size: 16px !important;
}

/* 选择器 */
.el-select,
.el-select__input,
.el-select-dropdown,
.el-select-dropdown__item,
.el-option,
.el-option span {
  font-size: 16px !important;
}

/* 级联选择器 */
.el-cascader,
.el-cascader__input,
.el-cascader-panel .el-cascader-node__label,
.el-cascader-menu .el-cascader-node__label,
.el-cascader__dropdown .el-cascader-node__label {
  font-size: 16px !important;
}

/* 日期选择器 */
.el-date-editor,
.el-date-editor__input,
.el-picker-panel,
.el-picker-panel__content,
.el-date-table td span,
.el-month-table td,
.el-year-table td {
  font-size: 16px !important;
}

/* 分页器 */
.el-pagination,
.el-pagination button,
.el-pagination span,
.el-pagination .el-select .el-input__inner,
.el-pagination .el-input__inner {
  font-size: 16px !important;
}

/* 标签 */
.el-tag,
.el-tag span {
  font-size: 16px !important;
}

/* 下拉菜单 */
.el-dropdown,
.el-dropdown-menu,
.el-dropdown-menu__item {
  font-size: 16px !important;
}

/* 复选框和单选框 */
.el-checkbox,
.el-checkbox__label,
.el-radio,
.el-radio__label {
  font-size: 16px !important;
}

/* 开关 */
.el-switch__label {
  font-size: 16px !important;
}

/* 步骤条 */
.el-steps,
.el-step__title,
.el-step__description {
  font-size: 16px !important;
}

/* 标签页 */
.el-tabs,
.el-tabs__item,
.el-tab-pane {
  font-size: 16px !important;
}

/* 面包屑 */
.el-breadcrumb,
.el-breadcrumb__item,
.el-breadcrumb__inner {
  font-size: 16px !important;
}

/* 菜单 */
.el-menu,
.el-menu-item,
.el-submenu__title {
  font-size: 16px !important;
}

/* 禁用菜单的斑马线效果 */
.el-menu .el-menu-item:nth-child(odd),
.el-menu .el-menu-item:nth-child(even),
.el-menu-item:nth-child(odd),
.el-menu-item:nth-child(even) {
  background-color: transparent !important;
}

.el-sub-menu .el-menu-item:nth-child(odd),
.el-sub-menu .el-menu-item:nth-child(even),
.el-menu .el-sub-menu .el-menu-item:nth-child(odd),
.el-menu .el-sub-menu .el-menu-item:nth-child(even) {
  background-color: transparent !important;
}

/* 确保侧边栏菜单没有斑马线效果 */
.sidebar-container .el-menu .el-menu-item:nth-child(odd),
.sidebar-container .el-menu .el-menu-item:nth-child(even),
.sidebar-container .el-sub-menu .el-menu-item:nth-child(odd),
.sidebar-container .el-sub-menu .el-menu-item:nth-child(even) {
  background-color: transparent !important;
}

/* 对话框 */
.el-dialog,
.el-dialog__header,
.el-dialog__title,
.el-dialog__body {
  font-size: 16px !important;
}

/* 抽屉 */
.el-drawer,
.el-drawer__header,
.el-drawer__title,
.el-drawer__body {
  font-size: 16px !important;
}

/* 消息提示 */
.el-message,
.el-notification,
.el-alert {
  font-size: 16px !important;
}

/* 工具提示 */
.el-tooltip__popper {
  font-size: 16px !important;
}

/* 气泡确认框 */
.el-popconfirm,
.el-popover {
  font-size: 16px !important;
}

/* 树形控件 */
.el-tree,
.el-tree-node,
.el-tree-node__content,
.el-tree-node__label,
.el-tree-select {
  font-size: 16px !important;
}

/* 卡片 */
.el-card,
.el-card__header,
.el-card__body {
  font-size: 16px !important;
}

/* 折叠面板 */
.el-collapse,
.el-collapse-item,
.el-collapse-item__header,
.el-collapse-item__content {
  font-size: 16px !important;
}

/* 时间线 */
.el-timeline,
.el-timeline-item,
.el-timeline-item__content {
  font-size: 16px !important;
}

/* 进度条 */
.el-progress,
.el-progress__text {
  font-size: 16px !important;
}

/* 评分 */
.el-rate {
  font-size: 16px !important;
}

/* 滑块 */
.el-slider {
  font-size: 16px !important;
}

/* 穿梭框 */
.el-transfer,
.el-transfer-panel,
.el-transfer-panel__header,
.el-transfer-panel__body,
.el-transfer-panel__item {
  font-size: 16px !important;
}

/* 上传 */
.el-upload,
.el-upload-list,
.el-upload-list__item {
  font-size: 16px !important;
}

/* 颜色选择器 */
.el-color-picker {
  font-size: 16px !important;
}

/* 描述列表 */
.el-descriptions,
.el-descriptions__header,
.el-descriptions__title,
.el-descriptions__body,
.el-descriptions-item,
.el-descriptions-item__label,
.el-descriptions-item__content {
  font-size: 16px !important;
}

/* 结果 */
.el-result,
.el-result__title,
.el-result__subtitle {
  font-size: 16px !important;
}

/* 骨架屏 */
.el-skeleton {
  font-size: 16px !important;
}

/* 空状态 */
.el-empty,
.el-empty__description {
  font-size: 16px !important;
}

/* 统计数值 */
.el-statistic,
.el-statistic__content {
  font-size: 16px !important;
}

/* 统一设置表单 label-width */
.el-form {
  /* 默认 label-width 设置为 100px */
  --el-form-label-width: 100px;
}

/* 针对内联表单（筛选条件）的 label-width 设置 */
.el-form.el-form--inline,
.el-form[inline],
.search-form {
  .el-form-item__label {
    width: 100px !important;
    min-width: 100px !important;
  }
}

/* 针对较长标签文字的特殊处理 */
.el-form-item__label {
  /* 如果标签文字超过4个字符，自动调整宽度 */
  min-width: 100px;

  /* 针对5-6个字符的标签 */
  &[title*="参数名称"],
  &[title*="参数键名"],
  &[title*="系统内置"],
  &[title*="字典名称"],
  &[title*="字典类型"],
  &[title*="角色名称"],
  &[title*="权限字符"],
  &[title*="用户名称"],
  &[title*="登录地址"],
  &[title*="资料名称"],
  &[title*="资料年份"],
  &[title*="资料质地"],
  &[title*="所在仓库"],
  &[title*="当前书架"],
  &[title*="书架名称"] {
    width: 120px !important;
    min-width: 120px !important;
  }
}
