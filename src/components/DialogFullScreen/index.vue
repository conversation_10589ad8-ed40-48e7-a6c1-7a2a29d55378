<template>
  <div>
    <el-dialog
      :style="cssVars"
      class="fullscreen-dialog"
      v-bind="$attrs"
      :fullscreen="dialogFull"
      :show-close="true"
      @closed="reset"
      append-to-body
    >
      <template #header>
        <div class="avue-crud__dialog__header" ref="dialogHeaderRef">
          <div class="el-dialog__title">
            <span v-if="title">{{ title }}</span>
            <slot name="customTitle" else> </slot>
          </div>

          <div class="avue-crud__dialog__menu" style="font-size: 20px">
            <el-icon :size="16" class="v-m-r-18 hover" @click="onFullScreen">
              <Crop v-if="dialogFull" /><FullScreen v-else />
            </el-icon>
          </div>
        </div>
      </template>
      <slot></slot>
      <template #footer>
        <slot name="footer"></slot>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="dialogFullScreen">
import { ref, computed, nextTick } from "vue";
import { ElDialog } from "element-plus";

import "element-plus/theme-chalk/el-dialog.css";

// 为 emits 添加类型

const emits = defineEmits(["getFullScreen", "close"]);

const props = defineProps(["title", "dialogHeight", "type"]);

const dialogFull = ref(false);
const dialogHeaderRef = ref(null);

const reset = () => {
  dialogFull.value = false;
};

const onFullScreen = () => {
  dialogFull.value = !dialogFull.value;
  emits("getFullScreen", dialogFull.value);
  resizeChart();
};

const close = () => {
  emits("close");
  emits("getFullScreen", dialogFull.value);
};

const resizeChart = () => {
  nextTick(() => {
    [".ve-line", ".ve-histogram"].forEach((selector) => {
      const domLi = document.querySelectorAll(selector);
      domLi.forEach((item) => {
        const instance = item.__vue__.$children[0].resize;
        instance.resize();
      });
    });
  });
};

const cssVars = computed(() => {
  const height = dialogHeaderRef.value?.scrollHeight || 0;
  return {
    "--dialogHeight": dialogFull.value
      ? `calc(100vh - ${height + 31}px)`
      : props.dialogHeight,
  };
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.fullscreen-dialog {
  .hover {
    color: $--color-info;
    cursor: pointer;
    &:hover {
      color: $--color-primary;
    }
  }

  :v-deep .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }

  .el-dialog__header {
    padding: 15px 20px 15px;
  }

  .el-dialog__title {
    width: calc(100% - 50px);
  }

  :v-deep .el-dialog__body {
    max-height: var(--dialogHeight);
  }
  .avue-crud__dialog__header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    // -webkit-box-align: center;
    // -ms-flex-align: center;
    // align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}
</style>
