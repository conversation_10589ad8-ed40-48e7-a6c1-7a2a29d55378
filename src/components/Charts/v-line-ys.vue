<template>
  <div :id="idBox" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup name="VLineYs">
let myChart = null;
import { numFormat } from "@/utils/index";
import * as echarts from "echarts";
import {
  ref,
  defineProps,
  getCurrentInstance,
  watch,
  onMounted,
  onBeforeUnmount,
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  idBox: {
    type: String,
    default: "barBox",
  },
  width: {
    type: String,
    default: "300px",
  },
  height: {
    type: String,
    default: "300px",
  },
  xTextVertical: {
    //x轴文本垂直展示
    type: Boolean,
    default: false,
  },
  rotate: {
    //x轴倾斜角度
    type: Number,
    default: 0,
  },
  ySplitLine: {
    //y轴引导线
    type: Boolean,
    default: true,
  },
  legendShow: {
    //显示图例
    type: Boolean,
    default: true,
  },
  gridOpt: {
    type: Object,
    default: () => {
      return {
        top: 50,
        left: "10%",
        right: "10%",
        bottom: 20,
        containLabel: true,
      };
    },
  },
  colorsProps: {
    type: [Array, Object],
    default: () => ["#0086D0", "#62F1FF", "#50E9AE", "#00C800", "#2AE876"],
  },
  bgColor: {
    type: String,
    default: "#06163D",
  },
  seriesOpt: {
    type: Array,
    default: null,
  },
  yAxisFormatter: {
    //显示百分比
    type: Boolean,
    default: false,
  },
  yUnit: {
    //单位
    type: String,
    default: "",
  },
  yUnit2: {
    //单位
    type: String,
    default: "",
  },
  // Y轴过滤小数，比如单位为个、人、辆等
  minInterval: {
    type: Number,
    default: 1,
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const option = ref({});
const xAxisTnterval = ref("auto");

watch(
  () => props.data,
  (newVal, oldVal) => {
    initChart();
  },
  { deep: true }
);

const initChart = () => {
  option.value = {
    color: props.colorsProps,
    backgroundColor: props.bgColor,
    legend: {
      show: props.legendShow,
      top: 0,
      textStyle: {
        // color: "#fff",
        fontSize: 14,
      },
    },
    tooltip: {
      show: true,
      trigger: "axis",
      backgroundColor: "rgba(0, 28, 67, 1)",
      borderColor: "#009AFF",
      padding: 20,
      textStyle: {
        fontSize: 12,
        color: "#fff",
      },
      axisPointer: {
        lineStyle: {
          color: "#009AFF",
          opacity: 0.5,
        },
      },
    },
    grid: props.gridOpt,
    xAxis: {
      type: "category",
      axisLabel: {
        textStyle: {
          color: "#5b97d5",
          // color: "#fff",
        },
        interval: xAxisTnterval,
        rotate: props.rotate,
      },
      axisPointer: {
        show: true,
        type: "line",
      },
      axisLine: {
        lineStyle: {
          color: "#5b97d5",
          width: 1,
          type: "solid",
        },
      },
      axisTick: {
        show: false,
      },
      data: getXData(),
    },
    yAxis: [
      {
        type: "value",
        name: props.yUnit,
        minInterval: props.minInterval,
        nameTextStyle: {
          color: "#5b97d5",
          // color: "#fff",
          fontSize: 12,
        },
        axisLabel: {
          formatter: function (value) {
            return numFormat(value);
          },
          textStyle: {
            color: "#5b97d5",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#5b97d5",
          },
        },
        splitLine: {
          show: props.ySplitLine,
          lineStyle: {
            color: "#5b97d5",
          },
        },
      },
      {
        type: "value",
        name: props.yUnit2,
        minInterval: props.minInterval,
        nameTextStyle: {
          color: "#5b97d5",
          // color: "#fff",
          fontSize: 12,
        },
        axisLabel: {
          formatter: function (value) {
            return numFormat(value);
          },
          textStyle: {
            color: "#5b97d5",
          },
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#5b97d5",
          },
        },
      },
    ],
    series: getSeries(),
  };
  if (myChart == null) {
    myChart = echarts.init(document.getElementById(props.idBox));
    option.value && myChart.setOption(option.value);
  } else {
    option.value && myChart.setOption(option.value);
  }
};

const getXData = () => {
  let target = props.data.columns[0];
  let _arr = [];
  props.data.rows.map((item) => {
    for (let key in item) {
      if (key == target) {
        _arr.push(item[key]);
      }
    }
  });

  if (_arr.length <= 19) { // 19地市全显示
    xAxisTnterval.value = 0;
  } else {
    xAxisTnterval.value = "auto";
  }
  return _arr;
};

const getSeries = () => {
  if (props.seriesOpt) {
    return props.seriesOpt;
  } else {
    let series = [];
    let columns = props.data.columns;
    series.push(
      {
        name: columns[1],
        type: "line",
        yAxisIndex: 1,
        data: props.data.rows.map((val) => {
          return val[columns[1]];
        }),
        tooltip: {
          valueFormatter: (value) => numFormat(value) + props.yUnit2,
        },
      },
      {
        name: columns[2],
        type: "line",
        data: props.data.rows.map((val) => {
          return val[columns[2]];
        }),
        tooltip: {
          valueFormatter: (value) => numFormat(value) + props.yUnit,
        },
      }
    );

    return series;
  }
};

const handleResize = () => {
  myChart && myChart.resize();
};

onBeforeUnmount(() => {
  if (myChart) {
    myChart?.clear();
    myChart?.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", handleResize);
});

onMounted(() => {
  proxy.$nextTick(() => {
    myChart == null ? initChart() : "";
  });
  window.addEventListener("resize", handleResize);
});
</script>
