<template>
  <div :id="idBox" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup name="VLine">
let myChart = null;
import { numFormat } from "@/utils/index";
import * as echarts from "echarts";
import {
  ref,
  defineProps,
  getCurrentInstance,
  watch,
  onMounted,
  onBeforeUnmount,
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  idBox: {
    type: String,
    default: "lineBox",
  },
  width: {
    type: String,
    default: "300px",
  },
  height: {
    type: String,
    default: "300px",
  },
  rotate: {
    //x轴倾斜角度
    type: Number,
    default: 0,
  },
  xLineColor: {
    //X轴坐标轴颜色
    type: String,
    default: "#ddd",
  },
  xTextColor: {
    //X轴坐标轴字体颜色
    type: String,
    default: "#6e7079",
  },
  xTextVertical: {
    //x轴文本垂直展示
    type: Boolean,
    default: false,
  },
  ySplitLine: {
    //y轴引导线
    type: Boolean,
    default: true,
  },
  fontSize: {
    type: Number,
    default: 12,
  },
  smooth: {
    // 曲线
    type: Boolean,
    default: true,
  },
  yAxisLineShow: {
    //Y坐标轴是否显示
    type: Boolean,
    default: true,
  },
  yTextColor: {
    //Y轴坐标轴字体颜色
    type: String,
    default: "#6e7079",
  },
  yLineColor: {
    //y轴坐标轴颜色
    type: String,
    default: "#ddd",
  },
  ySplitLineColor: {
    //Y轴网格线颜色
    type: String,
    default: "#ddd",
  },
  seriesLabel: {
    // 折线对应点数值
    type: Boolean,
    default: false,
  },
  legendIcon: {
    type: String,
    default: "",
  },
  areaStyle: { type: Boolean, default: true }, //渐变面积图
  symbolSize: { type: Number, default: 4 }, //折线点的大小
  areaStyleLastOffset: { type: Number, default: 1 },
  gridOpt: {
    type: Object,
    default: () => {
      return {
        top: 20,
        left: "3%",
        right: "1%",
        bottom: 0,
        containLabel: true,
      };
    },
  },
  colorsProps: {
    type: Array,
    default: () => ["#00F3D8", "#FFC24F", "#00D8EA", "#00D8EA"],
  },
  bgColor: {
    type: String,
    default: "#fff",
  },
  data: {
    type: Object,
    default: () => {},
  },
  yAxisFormatter: {
    //显示百分比
    type: Boolean,
    default: false,
  },
  legendShow: {
    //显示图例
    type: Boolean,
    default: true,
  },
  legendColor: {
    // 显示图例颜色
    type: String,
    default: "#6e7079",
  },
  seriesOpt: {
    type: Array,
    default: () => [],
  },
  symbol: {
    // 空心/实心圆点设置  默认显示实心圆点
    type: String,
    default: "circle",
  },
  yAxisUnitShow: {
    //Y轴顶端的单位是否显示
    type: Boolean,
    default: true,
  },
  yUnit: {
    //单位
    type: String,
    default: "",
  },
  // Y轴过滤小数，比如单位为个、人、辆等
  minInterval: {
    type: Number,
    default: 1,
  },
  // 保留小数点
  precision: {
    type: Number,
    default: 2,
  },
  markLine: {
    type: Object,
    default: () => {},
  },
  showMinMax: {
    type: Boolean,
    default: true
  },
  selectLegend: {
    type: Object,
    default: () => {}
  },
  yMinMax: {
    type: Boolean,
    default: false
  },
  valueUnit: {
    type: Boolean,
    default: true
  },
});
const option = ref({});
const xAxisTnterval = ref("auto");

watch(
  () => props.data,
  (newVal, oldVal) => {
    initChart();
  },
  { deep: true }
);

const initChart = () => {
  option.value = {
    color: props.colorsProps,
    backgroundColor: props.bgColor,
    tooltip: {
      show: true,
      trigger: "axis",
      // backgroundColor: "rgba(0, 28, 67, 1)",
      // borderColor: "#009AFF",
      textStyle: {
        fontSize: props.fontSize,
        // color: "#fff",
      },
      // axisPointer: {
      //   lineStyle: {
      //     color: "#009AFF",
      //     type: "dashed",
      //     opacity: 0.5,
      //   },
      // },
      valueFormatter: (value) =>
        numFormat(value, props.precision) + (props.valueUnit ? props.yUnit : ""),
    },
    grid: props.gridOpt,
    legend: {
      show: props.legendShow,
      top: 0,
      icon: props.legendIcon,
      textStyle: {
        color: props.legendColor,
        fontSize: props.fontSize,
      },
      itemStyle: {
        color: "inherit",
      },
      selected: props.selectLegend
    },
    xAxis: {
      type: "category",
      axisLabel: {
        interval: xAxisTnterval,
        fontSize: props.fontSize,
        rotate: props.rotate,
        formatter: props.xTextVertical
          ? (v) => v.split("").join("\n")
          : (v) => v,
        showMaxLabel: props.showMinMax, //显示最大值
        showMinLabel: props.showMinMax, //显示最小值
        textStyle: {
          color: props.xTextColor,
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: props.xLineColor,
          width: 1,
          type: "solid",
        },
      },
      axisTick: {
        show: false,
      },
      data: getXData(),
    },
    yAxis: {
      name: props.yAxisUnitShow ? props.yUnit : "",
      minInterval: props.minInterval,
      nameTextStyle: {
        color: "#fff",
        fontSize: props.fontSize,
      },
      splitLine: {
        show: props.ySplitLine,
        lineStyle: {
          color: props.ySplitLineColor,
          width: 1,
          opacity: 0.4,
        },
      },
      min: (value) => {
        return props.yMinMax ? value.min - 5 : null
      },
      max: (value) => {
        return props.yMinMax ? value.max + 5 : null
      },
      axisLabel: {
        color: props.yTextColor,
        fontSize: props.fontSize,
        formatter: function (value) {
          return numFormat(value, props.precision);
        },
      },
      axisLine: {
        show: props.yAxisLineShow,
        lineStyle: {
          color: props.yLineColor,
          width: 1,
          type: "solid",
        },
      },
    },
    series: getSeries(),
  };
  // if (myChart == null) {
  //   echarts?.init(document.getElementById(props.idBox))?.dispose();
  //   myChart = echarts.init(document.getElementById(props.idBox));
  //   option.value && myChart.setOption(option.value);
  // } else {
  //   option.value && myChart.setOption(option.value);
  // }
  echarts?.init(document.getElementById(props.idBox))?.dispose();
  myChart = echarts.init(document.getElementById(props.idBox));
  option.value && myChart.setOption(option.value);
};

const getXData = () => {
  let target = props.data.columns[0];
  let _arr = [];
  props.data.rows.map((item) => {
    for (let key in item) {
      if (key == target) {
        _arr.push(item[key]);
      }
    }
  });

  if (_arr.length <= 19) { // 19地市全显示
    xAxisTnterval.value = 0;
  } else {
    xAxisTnterval.value = "auto";
  }
  return _arr;
};

const getSeries = () => {
  if (props.seriesOpt.length) {
    return props.seriesOpt;
  } else {
    let series = [];
    props.data.columns.slice(1).map((cl, i) => {
      series.push({
        name: cl,
        type: "line",
        smooth: props.smooth,
        symbol: props.symbol,
        symbolSize: props.symbolSize,
        label: {
          show: props.seriesLabel,
          fontSize: props.fontSize,
        },
        areaStyle: props.areaStyle
          ? {
              opacity: 0.2,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: props.colorsProps[i], // 0% 处的颜色
                  },
                  {
                    offset: props.areaStyleLastOffset,
                    color: "rgba(4, 169, 245, 0)", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            }
          : {},
        data: props.data.rows.map((val) => {
          return val[cl];
        }),
        markLine: props.markLine,
      });
    });

    return series;
  }
};

const handleResize = () => {
  myChart && myChart.resize();
};

onBeforeUnmount(() => {
  if (myChart) {
    myChart?.clear();
    myChart?.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", handleResize);
});

onMounted(() => {
  proxy.$nextTick(() => {
    myChart == null ? initChart() : "";
  });
  window.addEventListener("resize", handleResize);
});
</script>
