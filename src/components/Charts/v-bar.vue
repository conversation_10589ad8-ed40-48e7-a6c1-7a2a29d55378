<template>
  <div :id="idBox" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup name="VBar">
let myChart = null;
import { numFormat } from "@/utils/index";
import * as echarts from "echarts";
import {
  ref,
  defineProps,
  getCurrentInstance,
  watch,
  onMounted,
  onBeforeUnmount,
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  idBox: {
    type: String,
    default: "barBox",
  },
  width: {
    type: String,
    default: "300px",
  },
  height: {
    type: String,
    default: "300px",
  },
  xTextVertical: {
    //x轴文本垂直展示
    type: Boolean,
    default: false,
  },
  rotate: {
    //x轴倾斜角度
    type: Number,
    default: 0,
  },
  xLineColor: {
    //X轴坐标轴颜色
    type: String,
    default: "#ddd",
  },
  xTextColor: {
    //X轴坐标轴字体颜色
    type: String,
    default: "#6e7079",
  },
  yTextColor: {
    //Y轴坐标轴字体颜色
    type: String,
    default: "#6e7079",
  },
  yLineColor: {
    //y轴坐标轴颜色
    type: String,
    default: "#ddd",
  },
  ySplitLine: {
    //y轴引导线
    type: Boolean,
    default: true,
  },
  yAxisLineShow: {
    //是否显示Y轴坐标轴轴线
    type: Boolean,
    default: true,
  },
  ySplitLineColor: {
    //Y轴网格线颜色
    type: String,
    default: "#eee",
  },
  legendShow: {
    //显示图例
    type: Boolean,
    default: true,
  },
  legendColor: {
    // 显示图例颜色
    type: String,
    default: "#6e7079",
  },
  gridOpt: {
    type: Object,
    default: () => {
      return {
        top: 50,
        left: "5%",
        right: "5%",
        bottom: 10,
        containLabel: true,
      };
    },
  },
  colorsProps: {
    type: [Array, Object],
    default: () => ["#5470c6", "#0086D0", "#62F1FF", "#50E9AE", "#00C800", "#2AE876"],
  },
  bgColor: {
    type: String,
    default: "#fff",
  },
  seriesOpt: {
    type: Array,
    default: null,
  },
  yAxisFormatter: {
    //显示百分比
    type: Boolean,
    default: false,
  },
  yUnit: {
    //单位
    type: String,
    default: "",
  },
  yUnit2: {
    //单位
    type: String,
    default: "",
  },
  // Y轴过滤小数，比如单位为个、人、辆等
  minInterval: {
    type: Number,
    default: 1,
  },
  // X轴数量过多，间隔显示
  intervalNum: {
    type: Number,
    default: 0,
  },
  // 柱宽
  barWidth: {
    type: String,
    default: "",
  },
  barMaxWidth: {
    type: String,
    default: "90px",
  },
  barColor: {
    //柱-颜色
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {},
  },
  // 保留小数点
  precision: {
    type: Number,
    default: 2,
  },
  // 是否显示背景图
  showBackground: {
    type: Boolean,
    default: true,
  },
});
const option = ref({});

watch(
  () => props.data,
  (newVal, oldVal) => {
    initChart();
  },
  { deep: true }
);

const initChart = () => {
  option.value = {
    color: props.colorsProps,
    backgroundColor: props.bgColor,
    legend: {
      show: props.legendShow,
      top: 0,
      textStyle: {
        color: props.legendColor,
        fontSize: 14,
      },
    },
    tooltip: {
      show: true,
      trigger: "axis",
      // backgroundColor: "rgba(0, 28, 67, 1)",
      // borderColor: "#009AFF",
      padding: 20,
      textStyle: {
        fontSize: 12,
        // color: "#fff",
      },
      // axisPointer: {
      //   lineStyle: {
      //     color: "#009AFF",
      //     type: "dashed",
      //     opacity: 0.5,
      //   },
      // },
      valueFormatter: (value) =>
        numFormat(value, props.precision) + props.yUnit,
    },
    grid: props.gridOpt,
    xAxis: {
      type: "category",
      axisLabel: {
        textStyle: {
          color: props.xTextColor,
        },
        interval:
          getXData().length > 20 && props.intervalNum ? props.intervalNum : 0,
        rotate: props.rotate,
      },
      axisLine: {
        lineStyle: {
          color: props.xLineColor,
        },
      },
      axisTick: {
        show: false,
      },
      data: getXData(),
    },
    yAxis: {
      type: "value",
      name: props.yUnit,
      minInterval: props.minInterval,
      nameTextStyle: {
        color: props.yTextColor,
        fontSize: 12,
      },
      axisLabel: {
        formatter: function (value) {
          return numFormat(value, props.precision);
        },
        textStyle: {
          color: props.yTextColor,
        },
      },
      axisLine: {
        show: props.yAxisLineShow,
        lineStyle: {
          color: props.yLineColor,
        },
      },
      splitLine: {
        show: props.ySplitLine,
        lineStyle: {
          color: props.ySplitLineColor,
          opacity: 0.6,
        },
      },
    },
    series: getSeries(),
  };
  if (myChart == null) {
    myChart = echarts.init(document.getElementById(props.idBox));
    option.value && myChart.setOption(option.value);
  } else {
    option.value && myChart.setOption(option.value);
  }
};

const getXData = () => {
  let target = props.data.columns[0];
  let _arr = [];
  props.data.rows.map((item) => {
    for (let key in item) {
      if (key == target) {
        _arr.push(item[key]);
      }
    }
  });

  return _arr;
};

const getSeries = () => {
  if (props.seriesOpt) {
    return props.seriesOpt;
  } else {
    let series = [];
    props.data.columns.slice(1).map((cl, i) => {
      series.push({
        name: cl,
        type: "bar",
        barMaxWidth: props.barMaxWidth,
        barWidth: props.barWidth,
        showBackground: props.showBackground,
        itemStyle: {
          color: props.barColor ? props.barColor : props.colorsProps[i],
        },
        data: props.data.rows.map((val) => {
          return val[cl];
        }),
      });
    });

    return series;
  }
};

const handleResize = () => {
  myChart && myChart.resize();
};

onBeforeUnmount(() => {
  if (myChart) {
    myChart?.clear();
    myChart?.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", handleResize);
});

onMounted(() => {
  proxy.$nextTick(() => {
    myChart == null ? initChart() : "";
  });
  window.addEventListener("resize", handleResize);
});
</script>
