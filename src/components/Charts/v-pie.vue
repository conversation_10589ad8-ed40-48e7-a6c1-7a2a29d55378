<template>
  <div :id="idBox" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup name="VPie">
let myChart = null;
import { numFormat } from "@/utils/index";
import * as echarts from "echarts";
import {
  ref,
  defineProps,
  getCurrentInstance,
  watch,
  onMounted,
  onBeforeUnmount,
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  idBox: {
    type: String,
    default: "pieBox",
  },
  height: {
    type: String,
    default: "350px",
  },
  width: {
    type: String,
    default: "350px",
  },
  colorProps: {
    type: Array,
    default: () => [
      "#00F0B4",
      "#FCBF4E",
      "#FF5959",
      "#006DFF",
      "#57d9f8",
      "#8d48e4",
      "#04c091",
    ],
  },
  bgColor: {
    type: String,
    default: "#fff",
  },
  yUnit: {
    //单位
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {},
  },
  radius: {
    type: Array,
    default: () => ["20%", "70%"],
  },
  seriesLabel: {
    type: Object,
    default: () => {
      return {
        color: "#fff",
        lineHeight: 12,
        fontSize: "14px",
        // formatter: `{b}`,
        formatter: ({ name, value, percent }) => {
          console.log(name, value, percent);
          return `${name}\n ${value} \n${percent}%`;
        },
        alignTo: "edge",
        normal: {
          show: true,
        },
        minMargin: 0,
        edgeDistance: 25,
      };
    },
  },
  labelLayout: {
    type: Boolean,
    default: false,
  },
  formatterTooltip: {
    type: Function,
  },
  // 中间数据
  centerInfo: {
    type: Object,
    default: () => {},
  },
  showLabelLine: {
    type: Boolean,
    default: true,
  },
});

const option = ref({});

watch(
  () => props.data,
  (newVal, oldVal) => {
    initChart();
  },
  { deep: true }
);

const initChart = () => {
  option.value = {
    color: props.colorProps,
    backgroundColor: props.bgColor,
    title: props.centerInfo,
    tooltip: {
      trigger: "item",
      // backgroundColor: "rgba(0, 28, 67, 1)",
      // borderColor: "#009AFF",
      padding: 10,
      textStyle: {
        fontSize: 12,
        // color: "#fff",
      },
      // axisPointer: {
      //   lineStyle: {
      //     color: "#009AFF",
      //     type: "dashed",
      //     opacity: 0.5,
      //   },
      // },
      valueFormatter: (value) => {
        return numFormat(value) + props.yUnit;
      },
    },
    dataset: {
      dimensions: props.data.columns,
      source: props.data.rows,
    },
    series: [
      {
        type: "pie",
        radius: props.radius,
        // data: getSeries(),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        label: props.seriesLabel,
        labelLine: {
          show: props.showLabelLine,
          lineStyle: {
            width: 1,
          },
        },
      },
    ],
  };
  props.formatterTooltip &&
    (option.value.tooltip.formatter = props.formatterTooltip);

  !props.showLabelLine &&
    (option.value.series[0].label = {
      normal: {
        show: props.showLabelLine,
      },
    });
  if (myChart == null) {
    myChart = echarts.init(document.getElementById(props.idBox));
    option.value && myChart.setOption(option.value);
  } else {
    option.value && myChart.setOption(option.value);
  }
};

const getSeries = () => {
  let result = [];
  const columns = props.data.columns;
  props.data.rows.map((el) => {
    result.push({
      name: el[columns[0]],
      value: el[columns[1]],
    });
  });

  return result;
};

const handleResize = () => {
  myChart && myChart.resize();
};

onBeforeUnmount(() => {
  if (myChart) {
    myChart?.clear();
    myChart?.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", handleResize);
});

onMounted(() => {
  proxy.$nextTick(() => {
    myChart == null ? initChart() : "";
  });
  window.addEventListener("resize", handleResize);
});
</script>
