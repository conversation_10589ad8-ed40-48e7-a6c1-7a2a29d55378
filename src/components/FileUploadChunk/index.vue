<template>
  <div class="upload-file">
    <el-upload
      multiple
      action="#"
      :before-upload="handleBeforeUpload"
      :http-request="requestUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-change="handleFileChange"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUpload"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
      >
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger"
            >删除</el-link
          >
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {
  singleUpload,
  multipartinit,
  spiteFilePart,
  multipartmerge,
} from "@/api/system/oss";

const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "ppt", "txt", "pdf"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadId = ref("");
// const uploadFileUrl = ref(baseUrl + "/oss/upload/single"); // 上传文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

// watch(
//   () => props.modelValue,
//   async (val) => {
//     if (val) {
//       let temp = 1;
//       // 首先将值转为数组
//       let list;
//       if (Array.isArray(val)) {
//         list = val;
//       } else {
//         await listByIds(val).then((res) => {
//           list = res.data.map((oss) => {
//             oss = { name: oss.originalName, url: oss.url, ossId: oss.ossId };
//             return oss;
//           });
//         });
//       }
//       // 然后将数组转为对象数组
//       fileList.value = list.map((item) => {
//         item = { name: item.name, url: item.url, ossId: item.ossId };
//         item.uid = item.uid || new Date().getTime() + temp++;
//         return item;
//       });
//     } else {
//       fileList.value = [];
//       return [];
//     }
//   },
//   { deep: true, immediate: true }
// );
/** 覆盖默认上传行为 */
function requestUpload() {}
const handleFileChange = (file) => {
  let size = file.size / 1024 / 1024;
  if (size < 50) {
    let fileFormData = new FormData();
    fileFormData.append("file", file.raw);
    proxy.$modal.loading("正在上传文件，请稍候...");
    singleUpload(fileFormData)
      .then((res) => {
        if (res.code == 0) {
          fileList.value.push({
            name: res.data.fileName,
            fileName: res.data.fileName,
            fileSize: res.data.fileSize,
            fileSuffix: res.data.fileSuffix,
            fileUrl: res.data.fileUrl,
          });
          emit("update:modelValue", fileList.value);
        } else {
          fileList.value = [];
        }
        proxy.$modal.closeLoading();
      })
      .catch((err) => {
        fileList.value = [];
        proxy.$modal.closeLoading();
      });
  } else {
    const chunkSize = 50 * 1024 * 1024; // 50MB一片
    const chunkCount = Math.ceil(file.size / chunkSize); // 总片数
    let promiseList = createFileChunk(file.raw, chunkSize); // 每个片得信息

    let jsons = {
      fileName: file.name,
      fileSize: file.size,
      partCount: chunkCount,
      path: "library",
      useOriginalFilename: true,
    };
    let tempFileChunks = [];
    // 获取地址
    multipartinit(jsons)
      .then(async (res) => {
        if (res && res.code == 0) {
          promiseList.map((item, index) => {
            tempFileChunks.push({
              chunkNumber: index + 2,
              chunk: item,
              uploadId: res.data.uploadId,
              progress: 0,
              status: "—",
            });
          });
          proxy.$modal.loading("正在上传文件，请稍候...");
          await uploadChunkBase(tempFileChunks);

          proxy.$modal.closeLoading();

          let result = await multipartmerge(res.data.uploadId);
          if (result.code == 0) {
            proxy.$message.success("上传成功!");
            uploadId.value = result.data.uploadId;

            const uploadFile = {
              name: result.data?.originalFilename,
              fileName: result.data?.originalFilename,
              fileSize: result.data?.size,
              fileSuffix: result.data?.ext,
              fileUrl: result.data?.url,
            };
            fileList.value.push(uploadFile);
            emit("update:modelValue", [uploadFile]);
          } else {
            proxy.$message.warning("文件分片上传失败！");
            fileList.value = [];
          }
        } else {
          proxy.$message.warning("文件分片上传失败！");
          fileList.value = [];
        }
      })
      .catch((err) => {
        proxy.$message.warning("文件分片上传失败！");
        fileList.value = [];
      });
  }
};
const createFileChunk = (file, size = chunkSize) => {
  const fileChunkList = [];
  let count = 0;
  while (count < file.size) {
    fileChunkList.push({
      file: file.slice(count, count + size),
    });
    count += size;
  }
  return fileChunkList;
};
// 上传分片
const uploadChunkBase = (chunkList) => {
  let successCount = 0;
  let totalChunks = chunkList.length;
  return new Promise((resolve, reject) => {
    const handler = () => {
      if (chunkList.length) {
        const chunkItem = chunkList.shift();
        // 直接上传二进制，不需要构造 FormData，否则上传后文件损坏
        let fileFormData = new FormData();
        fileFormData.append("file", chunkItem.chunk.file);

        spiteFilePart(
          chunkItem.uploadId,
          chunkItem.chunkNumber,
          fileFormData
        ).then((response) => {
          if (response.code === 0) {
            successCount++;
            handler(); // 继续上传下一个分片
          }
        });
        // .catch((error) => {
        //   console.log(error, "error");
        // });
      }
      if (successCount >= totalChunks) {
        resolve({ code: 0, data: {}, msg: "" });
      }
    };
    // 并发
    const UPLOADREQUESTNUM = 3;
    for (let i = 0; i < UPLOADREQUESTNUM; i++) {
      handler();
    }
  });
};
// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(
        `文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`
      );
      return false;
    }
  }
  // 校检文件大小
  // if (props.fileSize) {
  //   const isLt = file.size / 1024 / 1024 < props.fileSize;
  //   if (!isLt) {
  //     proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
  //     return false;
  //   }
  // }
  proxy.$modal.loading("正在上传文件，请稍候...");
  number.value++;
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败");
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({
      name: res.data.fileName,
      url: res.data.url,
      ossId: res.data.ossId,
    });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.fileUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除文件
function handleDelete(index) {
  // let ossId = fileList.value[index].ossId;
  // delOss(ossId);
  fileList.value.splice(index, 1);
  emit("update:modelValue", fileList.value);
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", fileList.value);
    proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return name;
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].ossId) {
      strs += list[i].ossId + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
defineExpose({
  fileList
});
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
