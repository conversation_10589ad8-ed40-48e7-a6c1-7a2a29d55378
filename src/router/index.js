import { createWebHistory, createRouter } from "vue-router";
/* Layout */
import Layout from "@/layout/index.vue";
import adminLayout from "@/layout/admin";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "/portal/index",
  },

  {
    path: "/portal",
    component: Layout,
    redirect: "noredirect",
    children: [
      {
        path: "index",
        component: () => import("@/views/portal/home/<USER>"),
        name: "PortalIndex",
        meta: { title: "首页" },
      },
      {
        path: "electronic",
        component: () => import("@/views/portal/electronic/index.vue"),
        name: "PortalElectronic",
        meta: { title: "电子资料馆" },
      },
      {
        path: "electronic/list",
        component: () => import("@/views/portal/electronic/list.vue"),
        name: "PortalElectronicList",
        meta: { title: "电子资料馆列表" },
      },
      {
        path: "topic/:type",
        component: () => import("@/views/portal/topic/index.vue"),
        name: "PortalTopic",
        meta: { title: "专题" },
      },
      {
        path: "paper",
        component: () => import("@/views/portal/paper/index.vue"),
        name: "PortalPaper",
        meta: { title: "实体资料馆" },
      },
      {
        path: "paper/record",
        component: () => import("@/views/portal/paper/record.vue"),
        name: "PortalPaperRecord",
        meta: { title: "进馆借阅" },
      },
      {
        path: "question",
        component: () => import("@/views/portal/question/index.vue"),
        name: "PortalQuestion",
        meta: { title: "常见问题" },
      },
      {
        path: "notice",
        component: () => import("@/views/portal/notice/index.vue"),
        name: "PortalNotice",
        meta: { title: "最新公告" },
      },
      {
        path: "noticeDetails",
        component: () => import("@/views/portal/notice/details.vue"),
        name: "PortalNoticeDetails",
        meta: { title: "最新公告" },
      },
      {
        path: "news",
        component: () => import("@/views/portal/news/index.vue"),
        name: "PortalNews",
        meta: { title: "统计新闻" },
      },
      {
        path: "search",
        component: () => import("@/views/portal/searchResult/index.vue"),
        name: "PortalSearchResult",
        meta: { title: "查询结果" },
      },
      // 个人中心相关路由
      {
        path: "user/collect",
        component: () => import("@/views/portal/user/collect.vue"),
        name: "PortalUserCollect",
        meta: { title: "我的收藏" },
      },
      {
        path: "user/borrow",
        component: () => import("@/views/portal/user/borrow.vue"),
        name: "PortalUserBorrow",
        meta: { title: "我的借阅" },
      },
      {
        path: "user/picture",
        component: () => import("@/views/portal/imageOrVideo/image.vue"),
        name: "PortalUserPicture",
        meta: { title: "我的图文" },
      },
      {
        path: "user/video",
        component: () => import("@/views/portal/imageOrVideo/image.vue"),
        name: "PortalUserVideo",
        meta: { title: "我的影音" },
      },
      // 图文影音资料馆
      {
        path: "imageOrVideo",
        component: () => import("@/views/portal/imageOrVideo/index.vue"),
        name: "PortalImageOrVideo",
        meta: { title: "图文影音馆" },
      },
      // 图文影音资料馆
      {
        path: "picture",
        component: () => import("@/views/portal/imageOrVideo/image.vue"),
        name: "PortalPicture",
        meta: { title: "图文资料馆" },
      },
      // 图文影音资料馆
      {
        path: "video",
        component: () => import("@/views/portal/imageOrVideo/image.vue"),
        name: "PortalVideo",
        meta: { title: "影音资料馆" },
      },
      // 图文影音资料馆
      {
        path: "imageDesc",
        component: () => import("@/views/portal/imageOrVideo/imageDesc.vue"),
        name: "imageDesc",
        meta: { title: "图文详情" },
      },
    ],
  },
  {
    path: "/user",
    component: adminLayout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: adminLayout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: adminLayout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: adminLayout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/system/oss-config",
    component: adminLayout,
    hidden: true,
    permissions: ["system:oss:list"],
    children: [
      {
        path: "index",
        component: () => import("@/views/system/oss/config"),
        name: "OssConfig",
        meta: { title: "配置管理", activeMenu: "/system/oss" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: adminLayout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_CONTEXT_PATH),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
