<template>
  <router-view />
</template>

<script setup>
import useSettingsStore from "@/store/modules/settings";
import { handleThemeStyle } from "@/utils/theme";

onMounted(() => {
  document.body.style.setProperty("--el-color-primary", "#114F87");
  // 设置全局字体大小为16px
  document.body.style.setProperty("--el-font-size-base", "16px");
  document.body.style.setProperty("--el-font-size-small", "16px");
  document.body.style.setProperty("--el-font-size-large", "16px");
  document.body.style.setProperty("--el-font-size-extra-large", "16px");
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>
