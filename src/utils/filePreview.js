import { Base64 } from "js-base64";
const servefilePreviewPath = import.meta.env.VITE_KKFILEVIEW_URL;
const filePath = import.meta.env.VITE_FILE_BASE_URL;
// var url = 'http://127.0.0.1:8080/file/test.txt'; //要预览文件的访问地址
// window.open('http://127.0.0.1:8012/onlinePreview?url='+encodeURIComponent(base64Encode(url)));
// http://192.168.118.6:8086//zs-hntjypt/library/2024/08/16/深入学习贯彻习近平总书记关于党的建设的重要思想.pdf

// http://192.168.118.6:8086/zs-hntjypt/library/2024/08/16/%E6%B7%B1%E5%85%A5%E5%AD%A6%E4%B9%A0%E8%B4%AF%E5%BD%BB%E4%B9%A0%E8%BF%91%E5%B9%B3%E6%80%BB%E4%B9%A6%E8%AE%B0%E5%85%B3%E4%BA%8E%E5%85%9A%E7%9A%84%E5%BB%BA%E8%AE%BE%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%9D%E6%83%B3.pdf
export const filePreviewUrl = (url) => {
  const fileUrl = `${filePath}${url}`;
  if (fileUrl.endsWith(".htm") || fileUrl.endsWith(".html")) {
    return `${fileUrl}`;
  }

  return `${servefilePreviewPath}?url=${encodeURIComponent(
    Base64.encode(fileUrl)
  )}`;
};
//年鉴类文件预览 直接打开地址
export const openFile = (url) => {
  // if(url.endsWith('autorun.inf')){
  //   return window.open(`${filePath}${url.replace(new RegExp('autorun.inf$'),'indexch.htm')}`);
  // }
  window.open(filePreviewUrl(url));
};
