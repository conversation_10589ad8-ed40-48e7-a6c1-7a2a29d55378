import { sm2 } from "sm-crypto";

const env = import.meta.env.MODE;
// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  env === "production"
    ? "0499d836de09e747b96c139d8a6caa83ef063d815ced16c7f936bff76cd74b96fc361f92f750729aa5d0a580a912862dd90f656f5739e6f4eeccaf903f9f602712" // 生产公钥
    : "0435815d704c56ecffdb271b7414a8a22bd33644027c6cff67a56106ebc91fbc962a629f93dbdd6b58d7b8736925274d7c8b5d09a26d3b1076a9421260ef0b6c8c"; // 测试/本地

const privateKey =
  env === "production"
    ? "3d883a3156f38022c7b791277c03a02f570126174f5985e875f3d7adaccd8f3c"
    : "a0718a54b4fa02c51a705caa95867d8ad6b495067b27d17e0a6a25ef1048587f";
const cipherMode = 1;

export const encrypt = (str) => {
  let encryptPwd = sm2.doEncrypt(str, publicKey, cipherMode); // 加密

  return "04" + encryptPwd;
};
export const decrypt = (str, privateKey) => {
  try {
    if (str.indexOf("04") == 0) {
      str = str.slice(2);
    }
    let encryptPwd = sm2.doDecrypt(str, privateKey, cipherMode); // 解密
    return encryptPwd;
  } catch (error) {
    return null;
  }
};
