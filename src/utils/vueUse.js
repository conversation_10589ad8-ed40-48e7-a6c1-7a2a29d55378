// 此js将项目中常用的 数据操作进行封装，形成hooks，以方便复用
import { ref, onMounted, onBeforeMount } from "vue";
import { toReactive, useSessionStorage } from "@vueuse/core";

// 针对资料类目树的数据格式，进行封装，形成hooks，以方便复用
// 接口地址： http://192.168.118.4:40001/project/163/interface/api/13930

import { listTreeCategory } from "@/api/category";

// 将类别数据存入内存中。
const meterStorage = {
  electronic: [],
  material: [],
};
// const materialFetchLoading=false
// 材料分类的hooks 包括视频类、影音类，实体类
export function useMaterialCategory() {
  const category = reactive(meterStorage);
  // 电子资料类目
  const electronicCat = computed(() => {
    return category["electronic"];
  });
  // 获取类目类别数据
  const getCategory = async () => {
    const { electronic, material } = category;
    if (!electronic?.length) {
      const response = await listTreeCategory({ categoryType: 1 });
      if (response && response.code == 0) {
        // const resObj = response?.data.map((item) => ({
        //   id: item.id,
        //   label: item.label,
        //   showCount: item.showCount,
        //   children: item.children
        // })) || [];
        meterStorage.electronic = category.electronic = response?.data;
      }
    }
    if (!material?.length) {
      const responseT = await listTreeCategory({ categoryType: 2 });
      if (responseT && responseT.code == 0) {
        meterStorage.material = category.material = responseT?.data;
      }
    }
    // categoryStorage.value = toReactive(category);
    // console.log("实体类目数据", meterStorage);
  };
  function getCategoryList(name) {
    return category[name]?.children;
  }
  // 获取电子资料类目下的子类目数据，通过电子资料类目ID
  function getElectronicChildrenById(id) {
    const electronic = category["electronic"].find((item) => item.id == id);
    return electronic?.children || [];
  }
  // 根据类目ID获取类目名称--此方法暂无应用场景，暂不实现
  function getChildrenNameById() {}
  onBeforeMount(() => {
    getCategory();
  });
  return {
    category,
    electronicCat,
    getCategoryList,
    getElectronicChildrenById,
  };
}
// 针对列表数据进行封装，形成hooks，以方便复用
export function useFetchlist(
  fetchFun,
  defaultParams,
  defaultSize = 10,
  dataConversionFun = (data) => data
) {
  const list = reactive({
    current: 1,
    size: defaultSize, // 每页显示条数
    total: 10, // 总条数
    records: [],
  });
  const loading = ref(false);
  let params = defaultParams || {};

  const fetchList = async () => {
    loading.value = true;
    const newProps = Object.assign(
      {},
      {
        current: list.current || 1,
        size: list.size || "",
        ...params,
      }
    );
    Object.keys(newProps).forEach((key) => key || delete newProps[key]);
    const response = await fetchFun(newProps);
    if (response && response.code == 0) {
      list.current = response?.data.current;
      list.total = response?.data.total;
      const records = response?.data?.records || [];
      list.records = records.map((item, index) => {
        const res = dataConversionFun(item, index);
        res.index = index + 1;
        return res;
      });
      // console.log("列表数据", list);
    }
    loading.value = false;
  };
  const pageChange = (current, size) => {
    if (Object.prototype.toString.call(current) === "[object Object]") {
      current.current && (list.current = current.current);
      current.size && (list.size = current.size);
    } else {
      list.current = current;
      size && (list.size = size);
    }
    fetchList();
  };
  const queryList = (props = {}, isFristPage = true) => {
    isFristPage && (list.current = 1);
    params = Object.assign({}, params, props);
    fetchList();
  };

  onMounted(() => {
    fetchList();
  });
  // console.log("列表start数据--onMounted", list);

  return { list, loading, pageChange, queryList };
}
