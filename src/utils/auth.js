import Cookies from "js-cookie";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { getQueryObject } from "@/utils/index";
const TokenKey = "Admin-Token";

const ERROR_LOGIN_CODE = {
  99991100: "用户认证失败！",
  99991101: "用户登录信息失效或未进行登录!",
};

export function getToken() {
  const code =
    useRoute()?.query?.code || getQueryObject(window.location.href)?.code;
  const token =
    useRoute()?.query?.accessToken ||
    getQueryObject(window.location.href)?.accessToken;
  console.log(token, "useRoute()?.query?.accessToken");
  if (token) {
    removeToken();
    Cookies.set(TokenKey, token);
  }
  const accessToken = token || Cookies.get(TokenKey);
  if (code && code != "0") {
    ElMessage.error(ERROR_LOGIN_CODE?.code);
    window.location.href = import.meta.env.VITE_APP_SSO_LOGIN_URL;
  } else {
    return accessToken;
  }
}

export function setToken(token) {
  return Cookies.set(TokenKey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}
