/**
 * 日期格式化
 *
 * 1. 无参数：默认初始化当前时间
 * @example dayjs().format();                                       -  2020-09-08T13:42:32+08:00
 * @example dayjs().format('YYYY-MM-DD');                           -  2020-09-08
 * @example dayjs().format('YYYY-MM-DD HH:mm:ss');                  -  2020-09-08 13:47:12
 *
 * 2. 格式化日期时间
 * @example dayjs(value).format('YYYY-MM-DD');			            // 初始化日期
 * @example dayjs(value).format('YYYY-MM-DD HH:mm:ss');             // 初始化日期时间
 *
 * 2. 加减
 * @example dayjs().add(7, 'day').format('YYYY-MM-DD');                 // 2022-04-27 今天（2022-04-20）加上7天
 * @example dayjs().add(1, 'month').format('YYYY-MM-DD');               // 2022-05-20 今天（2022-04-20）加上一月
 * @example dayjs().subtract(2, 'year').format('YYYY-MM-DD');            // 2020-05-20 今天（2022-04-20）减去2年
 * @example dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss');   // 2022-04-20 14:03:39 今天现在（2022-04-20 16:03:39）减去2小时
 *
 * 3.相对时间，依赖relativeTime
 * @example dayjs().from(dayjs('1990')) // 2 years ago
 * @example dayjs().from(dayjs(), true) // 2 years
 * @example dayjs().fromNow()
 * @example dayjs().to(dayjs())
 *
 */

import * as dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import advancedFormat from "dayjs/plugin/advancedFormat";
import weekOfYear from "dayjs/plugin/weekOfYear";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
dayjs.extend(advancedFormat);
dayjs.extend(weekOfYear);

const config = {
  thresholds: [
    { l: "s", r: 1, d: "second" },
    { l: "m", r: 60 },
    { l: "mm", r: 59, d: "minute" },
    { l: "h", r: 60 },
    { l: "hh", r: 23, d: "hour" },
    { l: "d", r: 24 },
    { l: "dd", r: 1000000, d: "day" },
    // { l: "M", r: 31 },
    // { l: "MM", r: 12, d: "month" },
    // { l: "y", r: 24 },
    // { l: "yy", d: "year" },
  ],
};

dayjs.extend(relativeTime, config);

export default dayjs;

// dayjs.extend(relativeTime);
