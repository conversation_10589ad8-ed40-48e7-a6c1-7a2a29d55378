<template>
  <!-- <div class="home app-container"> -->
  <div :class="{ 'app-container': !isChildrenComponent }">
    <page-center isChildrenComponent>
      <column-block
        :url="zuixindianziziliao"
        :hasMore="isChildrenComponent ? true : false"
        :title="isChildrenComponent ? '资料概览' : '进馆借阅'"
        :moreClick="
          () => {
            router.push('/portal/paper/record');
          }
        "
      >
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          v-if="!isChildrenComponent"
        >
          <el-form-item label="资料名称：">
            <el-input
              v-model="form.materialName"
              placeholder="请输入要查询的内容"
              style="width: 220px"
              @keyup.enter="onSubmit"
              clearable
            />
          </el-form-item>
          <el-form-item label="书目编号">
            <el-input
              v-model="form.materialNo"
              placeholder="请输入要查询的内容"
              style="width: 220px"
              @keyup.enter="onSubmit"
              clearable
            />
          </el-form-item>

          <el-form-item label="印刷时间" style="width: 308px">
            <el-date-picker
              v-model="form.monthRange"
              value-format="YYYY-MM"
              type="monthrange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[
                new Date(2000, 1, 1, 0, 0, 0),
                new Date(2000, 1, 1, 23, 59, 59),
              ]"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <div>
          <el-table
            :data="list.records"
            border
            style="width: 100%; min-height: calc(100vh - 570px)"
            :style="{
              'min-height': isChildrenComponent
                ? ''
                : ' min-height: calc(100vh - 570px)',
            }"
          >
            <!-- <el-table-column
              align="center"
              prop="index"
              label="序号"
              width="90"
            /> -->
            <el-table-column
              align="center"
              prop="materialName"
              label="资料名称"
            />
            <!-- <el-table-column
              align="center"
              prop="materialNo"
              v-if="!isChildrenComponent"
              label="书目编号"
            /> -->
            <el-table-column
              align="center"
              prop="categoryNames"
              v-if="isChildrenComponent"
              label="分类"
            ></el-table-column>
            <el-table-column
              align="center"
              prop="materialCategoryName"
              v-if="!isChildrenComponent"
              label="分类"
            ></el-table-column>
            <!-- <el-table-column
              align="center"
              prop="printDate"
              label="印刷时间"
              v-if="!isChildrenComponent"
            /> -->
            <!-- <el-table-column
              v-if="!isChildrenComponent"
              align="center"
              label="馆藏分类"
            >
              <template #default="scope">
                <dict-tag
                  :options="electronic_cluster_type"
                  :value="scope.row.materialCluster"
                />
              </template>
            </el-table-column> -->
            <el-table-column
              v-if="isChildrenComponent"
              align="center"
              prop="warehouseName"
              label="所在仓库"
            />
            <el-table-column
              v-if="isChildrenComponent && userStore.id"
              align="center"
              prop="shelfName"
              label="所在书架"
            />
            <el-table-column
              align="center"
              v-if="isChildrenComponent && userStore.id"
              prop="realStock"
              label="库存"
            />
            <el-table-column
              align="center"
              v-if="!isChildrenComponent && userStore.id"
              prop="curStock"
              label="库存"
            />

            <el-table-column
              align="center"
              label="操作"
              width="120"
              v-if="!isChildrenComponent"
            >
              <template #default="scope">
                <record-or-collect-btn
                  :materialTexture="2"
                  :option="scope.row"
                  :click="handleClick"
                />
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box">
            <pagination
              v-show="list.total > 0"
              :total="list.total"
              v-model:page="list.current"
              v-model:limit="list.size"
              @pagination="pageChange"
            />
          </div>
        </div>
      </column-block>
    </page-center>
    <dialog-record @getList="queryList" ref="refRecordBook" />
  </div>
</template>

<script setup name="PortalElectronicList">
import zuixindianziziliao from "@/assets/images/zuixindianziziliao.png";
import dayjs from "dayjs";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import DialogRecord from "@/views/portal/components/DialogBtn/DialogRecord";
import RecordOrCollectBtn from "@/views/portal/components/RecordOrCollectBtn/RecordOrCollectBtn";
// import CategoryTag from "@/views/portal/components/Tag/CategoryTag";
import { replaceStr } from "@/utils/validate";
import {
  materialPage,
  materialPagePortal,
  physicalApply,
} from "@/api/material";
import { useFetchlist, useMaterialCategory } from "@/utils/vueuse";
import useUserStore from "@/store/modules/user";
const router = useRouter();
const userStore = useUserStore();
const { category } = useMaterialCategory();
const { proxy } = getCurrentInstance();
const { electronic_cluster_type } = proxy.useDict("electronic_cluster_type");

const props = defineProps({
  isChildrenComponent: {
    type: Boolean,
    default: false,
  },
});

const { list, pageChange, queryList } = useFetchlist(
  props.isChildrenComponent ? materialPagePortal : materialPage,
  {
    materialTexture: 2,
  },
  20
);

const form = reactive({
  materialNo: "",
  materialName: "",
  monthRange: [],
});

// 处理借阅点击事件
const handleClick = (item) => {
  proxy.$refs.refRecordBook.showDialog(item);
};

const onSubmit = () => {
  const params = toRaw(form);
  if (params.monthRange?.length) {
    params.printDateStar = dayjs(params.monthRange[0]).format("YYYY-MM-01");
    params.printDateEnd = dayjs(params.monthRange[1])
      .endOf("month")
      .format("YYYY-MM-DD");
  } else {
    params.printDateStar = "";
    params.printDateEnd = "";
  }
  queryList(params);
};
onMounted(() => {
  console.log(props.isChildrenComponent, "isChildrenComponent");
});
</script>

<style scoped lang="scss">
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
