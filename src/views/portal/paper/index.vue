<template>
  <div class="app-container home" v-if="!loading">
    <page-center class="v-m-t-15">
      <global-search />
    </page-center>
    <page-center class="v-m-t-40">
      <column-block :url="shitiziliaoguan" title="实体资料馆">
        <div class="paper-intro">
          <div class="v-m-r-10">
            <el-image
              style="width: 600px; height: 300px; border-radius: 6px"
              :src="dianziBanner"
              :fit="fit"
            />
          </div>
          <div>
            <div class="intro v-flex v-col-top">
              <el-icon class="v-m-t-5 v-m-r-10" color="#114F87" size="16"
                ><DArrowRight
              /></el-icon>
              <div>
                <div class="v-font-22" style="line-height: 32px">
                  实体图书馆收录了中国统计年鉴、外省年鉴、河南省年鉴和河南市县年鉴等多类统计资料。用户可通过线上查询系统高效检索并精准定位所需资料，并登录系统提交借阅申请。图书管理员将根据申请信息主动与用户取得联系，协助办理借阅手续。
                </div>
              </div>
            </div>

            <div class="v-m-l-40" style="padding-top: 40px">
              <dialog-btn
                @submit="onSubmit"
                title="借阅须知"
                :showCancelBtn="false"
              >
                <template #btn="slotProps">
                  <el-button type="default" @click="slotProps.showDialog()">
                    借阅须知
                  </el-button>
                </template>
                <template #content>
                  <el-descriptions title="" :column="1">
                    <el-descriptions-item label="开放时间:">{{
                      data.introduction?.time
                    }}</el-descriptions-item>
                    <el-descriptions-item label="借阅说明:">{{
                      data.introduction?.borrowExplain
                    }}</el-descriptions-item>
                  </el-descriptions>
                </template>
              </dialog-btn>
              <el-button
                style="margin-left: 10px"
                type="primary"
                @click="router.push('/portal/paper/record')"
                >进馆借阅</el-button
              >
            </div>
          </div>
        </div>
      </column-block>
    </page-center>

    <page-center padding="10px 0">
      <el-row :gutter="20">
        <el-col :span="24">
          <column-block :url="guancangfenlei" title="实体馆藏分类">
            <el-row :gutter="20" style="width: inherit">
              <el-col
                :span="6"
                v-for="(pItem, index) in category.material"
                :key="pItem.id"
              >
                <!-- <div
                  class="label-entrce v-pointer"
                  @click="handleSearch(pItem)"
                >
                  {{ pItem.label }}
                </div> -->
                <div
                  :class="[
                    'label-entrce',
                    Math.floor(index / 4) % 2 === 0 // 判断行号是偶数还是奇数
                      ? `label-entrce${index % 2}` // 偶数行：蓝红蓝红
                      : `label-entrce${(index + 1) % 2}`, // 奇数行：红蓝红蓝
                  ]"
                  class="v-pointer"
                  @click="handleSearch(pItem)"
                >
                  {{ pItem.label }}
                </div>
              </el-col>
            </el-row>
          </column-block>
        </el-col>
      </el-row>
    </page-center>

    <page-center padding="10px 0 20px">
      <!-- <el-row>
        <el-col :span="24">
          <column-block
            :url="dianzishu"
            :hasMore="true"
            title="实体概览"
            :moreClick="
              () => {
                router.push('/portal/paper/record');
              }
            "
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook03"
                  ></el-image>
                  <div class="title v-m-y-10">
                    仓库数量：{{ data.physicalSummary.warehouseNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook01"
                  ></el-image>
                  <div class="title v-m-y-10">
                    书架数量：{{ data.physicalSummary.shelfNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook08"
                  ></el-image>
                  <div class="title v-m-y-10">
                    资料数量：{{ data.physicalSummary.materialNum }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="v-m-t-20">
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook08"
                  ></el-image>
                  <div class="title v-m-y-10">
                    出库总量：{{ data.physicalSummary.outStockNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook01"
                  ></el-image>
                  <div class="title v-m-y-10">
                    入库总量：{{ data.physicalSummary.inStockNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook06"
                  ></el-image>
                  <div class="title v-m-y-10">
                    借阅总量：{{ data.physicalSummary.borrowNum }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </column-block>
        </el-col>
      </el-row> -->
      <recordBook isChildrenComponent />
    </page-center>

    <!-- <page-center padding="10px 0 20px">
      <el-row>
        <el-col :span="24">
          <column-block
            :url="guancangfenlei"
            :hasMore="true"
            title="馆藏概览"
            :moreClick="
              () => {
                router.push('/portal/paper/record');
              }
            "
          >
            <el-table
              :data="paperList.records"
              style="width: 100%"
              height="400px"
              border
            >
              <el-table-column
                align="center"
                prop="categoryName"
                label="资料类目"
              >
                <template #default="{ row }">
                  <el-link :underline="false" @click="goHighSearch(row)">{{
                    row.categoryName
                  }}</el-link>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                prop="categoryClass"
                label="类别"
              />
              <el-table-column
                align="center"
                prop="showCount"
                label="册（件）数"
              />
            </el-table>
          </column-block>
        </el-col>
      </el-row>
    </page-center> -->
  </div>
</template>
<script setup name="Paper">
import shitiziliaoguan from "@/assets/images/shitiziliaoguan.png";
import guancangfenlei from "@/assets/images/guancangfenlei.png";
// import dianzishu from "@/assets/images/dianzishu.png";
import dianziBanner from "@/assets/images/shitiBanner.png";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
// import TopicCard from "@/views/portal/components/Card/TopicCard";
import {
  paperStting,
  paperCategoryQuery,
  queryPhysicalSummary,
} from "@/api/paperHome";
import { materialType } from "@/utils/dataMap.js";
import { useFetchlist, useMaterialCategory } from "@/utils/vueUse";
import DialogBtn from "@/views/portal/components/DialogBtn/DialogBtn";
import { useDict } from "@/utils/dict";
import { tansParams } from "@/utils/ruoyi";
/* import ebook01 from "@/assets/images/ebook01.png";
import ebook03 from "@/assets/images/ebook03.png";
import ebook06 from "@/assets/images/ebook06.png";
import ebook08 from "@/assets/images/ebook08.png"; */
import recordBook from "./record";

// import { getCurrentInstance } from "vue";
// 一次性将几个常用的字段数据都加载吧，临时处理
const dict = useDict("physical_record_type", "electronic_cluster_type");
const { category } = useMaterialCategory();
const { list: paperList } = useFetchlist(
  paperCategoryQuery,
  {},
  50,
  function (item) {
    const physical_record_type_dict = dict["physical_record_type"]?.value || [];
    return {
      ...item,
      categoryLingual: physical_record_type_dict.find(
        (dicItem) => dicItem.value == item.categoryLingual
      )?.label,
      categoryClass: materialType[item.categoryClass],
    };
  }
);
const router = useRouter();
const paperCat = ref([]);
//
const data = reactive({
  //简介数据
  introduction: {},
  loading: false,
  physicalSummary: {},
});

const goHighSearch = (row) => {
  const query = {
    materialCategory: row.id,
    materialTexture: "2",
  };
  const path = `/portal/search?${tansParams(query)}`;
  router.push(path);
};

const fetchSettingQuery = async () => {
  const response = await paperStting({});
  if (response.code == 0 && response.data) {
    const reData = response.data;
    data.introduction = reData;
    data.introduction.time = `${reData.openHour}:${reData.openMinute}  至  ${reData.closeHour}:${reData.closeMinute}`;
  }
};

const fetchPhysicalSummary = async () => {
  const res = await queryPhysicalSummary();
  data.physicalSummary = res.data;
  console.log(data.physicalSummary, "data.physicalSummary");
};

const onSubmit = (done) => {
  done && done();
};

const handleSearch = (data) => {
  router.push({
    path: "/portal/search",
    query: { materialCategory: data.id, materialTexture: 2 },
  });
};

onMounted(() => {
  fetchPhysicalSummary();
  fetchSettingQuery();
});
</script>

<style scoped lang="scss">
.app-container {
  background: url("@/assets/images/bg-bottom.png") center bottom / 100% 222px
    no-repeat;
}
// 懒得封装组件了，先拷贝吧
.paper-intro {
  display: flex;
  flex-direction: row;
  justify-content: normal;
  // align-items: stretch;
  // background: #e3fdff;

  .intro {
    // margin-top: -7px;
    padding: 18px;
    flex: 1 0 0;
    // overflow-y: scroll;
    // background: #e3fdff;
    // height: 300px;
  }
}

.count-box {
  overflow: auto;
  text-align: center;
  background: linear-gradient(180deg, #5dd5ff 0%, #1d8ad4 100%);
  border-radius: 4px;

  .title {
    font-weight: 800;
    font-size: 18px;
    color: #fff;
  }
}

.label-entrce {
  // width: 100%;
  width: 287px;
  height: 118px;
  font-size: 24px;
  text-align: center;
  // background: linear-gradient(180deg, #5dd5ff 0%, #1d8ad4 100%);
  // border-radius: 8px;
  font-weight: 600;
  margin: 10px 0;
  // padding: 20px;
  font-weight: 800;
  font-size: 22px;
  color: #ffffff;
  line-height: 118px;
  text-shadow: 0px 2px 2px rgba(12, 19, 104, 0.5);
  overflow: hidden;
  // min-height: 73px;
}

.label-entrce0 {
  background: url("@/assets/images/guancang-item0.png") center / 100% 100%
    no-repeat;
}
.label-entrce1 {
  background: url("@/assets/images/guancang-item2.png") center / 100% 100%
    no-repeat;
}
.label-entrce2 {
  background: url("@/assets/images/guancang-item2.png") center / 100% 100%
    no-repeat;
}
</style>
