<template>
  <div class="app-container home" v-if="!loading">
    <page-center padding="0 0 10px">
      <global-search />
    </page-center>
    <page-center padding="10px 0">
      <column-block :url="shitiguan" title="实体资料馆">
        <div class="paper-intro">
          <el-image
            style="width: 600px; height: 300px"
            :src="dianziBanner"
            :fit="fit"
          />
          <div class="intro">
            <div>
              本分馆主要收集整理实体汇编、资料、年报等，共计{{
                data.introduction?.totalNum
              }}册。
            </div>
            <div style="margin-top: 10px">
              读者可通过：<span
                v-for="(item, index) in category.material"
                :key="item.id"
                >{{ index > 0 ? "、" : "" }}{{ item.label }}</span
              >等多种分类检索借阅。
            </div>
            <div style="padding-top: 40px">
              <dialog-btn
                @submit="onSubmit"
                title="借阅须知"
                :showCancelBtn="false"
              >
                <template #btn="slotProps">
                  <el-button type="default" @click="slotProps.showDialog()">
                    借阅须知
                  </el-button>
                </template>
                <template #content>
                  <el-descriptions title="" :column="1">
                    <el-descriptions-item label="开放时间:">{{
                      data.introduction?.time
                    }}</el-descriptions-item>
                    <el-descriptions-item label="借阅说明:">{{
                      data.introduction?.borrowExplain
                    }}</el-descriptions-item>
                  </el-descriptions>
                </template>
              </dialog-btn>
              <el-button
                style="margin-left: 10px"
                type="warning"
                @click="router.push('/portal/paper/record')"
                >进馆借阅</el-button
              >
            </div>
          </div>
        </div>
      </column-block>
    </page-center>

    <page-center padding="10px 0 20px">
      <el-row>
        <el-col :span="24">
          <column-block
            :url="dianzishu"
            :hasMore="true"
            title="实体概览"
            :moreClick="
              () => {
                router.push('/portal/paper/record');
              }
            "
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook03"
                  ></el-image>
                  <div class="title v-m-y-10">
                    仓库数量：{{ data.physicalSummary.warehouseNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook01"
                  ></el-image>
                  <div class="title v-m-y-10">
                    书架数量：{{ data.physicalSummary.shelfNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook08"
                  ></el-image>
                  <div class="title v-m-y-10">
                    资料数量：{{ data.physicalSummary.materialNum }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="v-m-t-20">
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook08"
                  ></el-image>
                  <div class="title v-m-y-10">
                    出库总量：{{ data.physicalSummary.outStockNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook01"
                  ></el-image>
                  <div class="title v-m-y-10">
                    入库总量：{{ data.physicalSummary.inStockNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="count-box">
                  <el-image
                    class="v-m-t-20"
                    style="height: 40px"
                    :src="ebook06"
                  ></el-image>
                  <div class="title v-m-y-10">
                    借阅总量：{{ data.physicalSummary.borrowNum }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <recordBook isChildrenComponent />
          </column-block>
        </el-col>
      </el-row>
    </page-center>

    <page-center padding="10px 0 20px">
      <el-row>
        <!-- <el-col :span="7">
          <topic-card cardType="paper"></topic-card>
        </el-col> -->
        <el-col :span="24">
          <column-block
            :url="guancang"
            :hasMore="true"
            title="馆藏概览"
            :moreClick="
              () => {
                router.push('/portal/paper/record');
              }
            "
          >
            <el-table
              :data="paperList.records"
              style="width: 100%"
              height="400px"
              border
            >
              <el-table-column
                align="center"
                prop="categoryName"
                label="资料类目"
              >
                <template #default="{ row }">
                  <el-link :underline="false" @click="goHighSearch(row)">{{
                    row.categoryName
                  }}</el-link>
                </template>
              </el-table-column>
              <!-- <el-table-column
                align="center"
                prop="categoryLingual"
                label="文种"
              /> -->
              <el-table-column
                align="center"
                prop="categoryClass"
                label="类别"
              />
              <el-table-column
                align="center"
                prop="showCount"
                label="册（件）数"
              />
            </el-table>
          </column-block>
        </el-col>
      </el-row>
    </page-center>
  </div>
</template>
<script setup name="Paper">
import shitiguan from "@/assets/images/shitiguan.png";
import guancang from "@/assets/images/guancang.png";
import dianzishu from "@/assets/images/dianzishu.png";
import dianziBanner from "@/assets/images/shitiBanner.png";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import TopicCard from "@/views/portal/components/Card/TopicCard";
import {
  paperStting,
  paperCategoryQuery,
  queryPhysicalSummary,
} from "@/api/paperHome";
import { materialType } from "@/utils/dataMap.js";

import { useFetchlist, useMaterialCategory } from "@/utils/vueUse";
import DialogBtn from "@/views/portal/components/DialogBtn/DialogBtn";
import { useDict } from "@/utils/dict";
import { tansParams } from "@/utils/ruoyi";
import ebook01 from "@/assets/images/ebook01.png";
import ebook03 from "@/assets/images/ebook03.png";
import ebook06 from "@/assets/images/ebook06.png";
import ebook08 from "@/assets/images/ebook08.png";
import recordBook from "./record";

// import { getCurrentInstance } from "vue";
// 一次性将几个常用的字段数据都加载吧，临时处理
const dict = useDict("physical_record_type", "electronic_cluster_type");
const { category } = useMaterialCategory();
const { list: paperList } = useFetchlist(
  paperCategoryQuery,
  {},
  50,
  function (item) {
    const physical_record_type_dict = dict["physical_record_type"]?.value || [];
    return {
      ...item,
      categoryLingual: physical_record_type_dict.find(
        (dicItem) => dicItem.value == item.categoryLingual
      )?.label,
      categoryClass: materialType[item.categoryClass],
    };
  }
);
// console.log(`paperList->`,paperList)
const router = useRouter();
//
const data = reactive({
  //简介数据
  introduction: {},
  loading: false,
  physicalSummary: {},
});

const goHighSearch = (row) => {
  const query = {
    materialCategory: row.id,
    materialTexture: "2",
  };
  const path = `/portal/search?${tansParams(query)}`;
  router.push(path);
};

const fetchSettingQuery = async () => {
  const response = await paperStting({});
  if (response.code == 0 && response.data) {
    const reData = response.data;
    data.introduction = reData;
    data.introduction.time = `${reData.openHour}:${reData.openMinute}  至  ${reData.closeHour}:${reData.closeMinute}`;
  }
};

const fetchPhysicalSummary = async () => {
  const res = await queryPhysicalSummary();
  data.physicalSummary = res.data;
  console.log(data.physicalSummary, "data.physicalSummary");
};

const onSubmit = (done) => {
  done && done();
};

onMounted(() => {
  fetchPhysicalSummary();
  fetchSettingQuery();
});
</script>

<style scoped lang="scss">
// 懒得封装组件了，先拷贝吧
.paper-intro {
  display: flex;
  flex-direction: row;
  justify-content: normal;
  // align-items: stretch;
  background: #e3fdff;

  .intro {
    // margin-top: -7px;
    padding: 18px;
    flex: 1 0 0;
    // overflow-y: scroll;
    background: #e3fdff;
    height: 300px;
  }
}

.count-box {
  overflow: auto;
  text-align: center;
  background: linear-gradient(180deg, #5dd5ff 0%, #1d8ad4 100%);
  border-radius: 4px;

  .title {
    font-weight: 800;
    font-size: 18px;
    color: #fff;
  }
}
</style>
