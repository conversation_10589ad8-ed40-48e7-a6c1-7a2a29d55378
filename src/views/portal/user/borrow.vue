<template>
  <div class="app-container home">
    <page-center padding="20px 0">
      <column-block title="我的借书">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="资料名称：">
            <el-input
              v-model="queryParams.materialName"
              placeholder="输入查找内容"
              style="width: 300px"
              clearable
            />
          </el-form-item>
          <el-form-item label="馆藏分类：">
            <el-select
              v-model="queryParams.materialCluster"
              placeholder="请选择馆藏分类"
              :default-first-option="true"
              style="width: 300px"
              clearable
            >
              <el-option
                v-for="dict in electronic_cluster_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="年份：" prop="materialYear">
            <el-date-picker
              v-model="queryParams.materialYear"
              type="year"
              placeholder="请选择资料年份"
              value-format="YYYY"
              style="width: 300px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="借阅状态：" prop="borrowStatus">
            <el-select
              v-model="queryParams.borrowStatus"
              placeholder="请选择借阅状态"
              :default-first-option="true"
              style="width: 300px"
              clearable
            >
              <el-option
                v-for="dict in borrow_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">筛选</el-button>
          </el-form-item>
        </el-form>
        <div>
          <el-table
            border
            :data="tableData"
            v-loading="loading"
            style="width: 100%"
          >
            <el-table-column
              align="center"
              prop="index"
              label="序号"
              width="150"
            >
              <template #default="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="materialName"
              label="名称"
              min-width="220"
            />
            <el-table-column
              align="center"
              prop="materialYear"
              label="年份"
              width="120"
            />
            <el-table-column
              align="center"
              prop="materialNo"
              label="书目编号"
              width="120"
            />
            <el-table-column
              align="center"
              prop="materialNo"
              label="借书期间"
              width="240"
            >
              <template #default="scope">
                {{ scope.row.borrowStartDate }} 至 {{ scope.row.borrowEndDate }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="realStock"
              label="库存"
              width="120"
            />
            <el-table-column
              align="center"
              prop="materialCluster"
              label="馆藏分类"
              width="120"
            >
              <template #default="scope">
                <dict-tag
                  style="display: inline"
                  :options="electronic_cluster_type"
                  :value="scope.row.materialCluster"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="borrowStatus"
              label="状态"
              width="120"
            >
              <template #default="scope">
                <dict-tag
                  style="display: inline"
                  :options="borrow_status"
                  :value="scope.row.borrowStatus"
                />
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="120">
              <template #default="scope">
                <el-button
                  v-if="scope.row.borrowStatus == 3"
                  link
                  type="primary"
                  size="small"
                  @click="handleCompleted(scope.row)"
                >
                  办结申请
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box">
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.current"
              v-model:limit="queryParams.size"
              @pagination="getList"
            />
          </div>
        </div>
      </column-block>
    </page-center>
    <el-dialog v-model="open" width="600px">
      <template #header>{{ title }}</template>
      <el-form
        ref="comForm"
        :model="completedForm"
        :rules="rules"
        label-width="80"
      >
        <div class="catemana-dialog-left">
          <el-form-item label="资料名称" prop="materialName">
            <el-input
              v-model="completedForm.materialName"
              placeholder="请输入资料名称"
              disabled
            />
          </el-form-item>
          <el-form-item label="借阅时间" prop="materialCategory">
            <el-date-picker
              v-model="completedForm.borrowDate"
              type="daterange"
              disabled
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="当前状态" prop="borrowStatus">
            <el-select
              v-model="completedForm.borrowStatus"
              placeholder="请选择当前状态"
              disabled
              style="width: 100%"
            >
              <el-option
                v-for="dict in borrow_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="情况说明" prop="abortExplain">
            <el-input
              v-model="completedForm.abortExplain"
              placeholder="请输入情况说明"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="资料类型" prop="materialType">
            <imageUpload
              v-model="completedForm.abortFileList"
              :fileSize="15"
              :limit="1"
            ></imageUpload>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit" v-if="status !== 2"
            >确 定</el-button
          >
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PortalPaperListRecord">
import { ElMessage } from "element-plus";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import { deepClone } from "@/utils/index";
import { getBorrowListFront, pushBorrowCompleted } from "@/api/user.js";

const { proxy } = getCurrentInstance();
const { electronic_cluster_type, physical_category, borrow_status } =
  proxy.useDict(
    "electronic_cluster_type",
    "physical_category",
    "borrow_status"
  );

// 字典数据
const data = reactive({
  loading: false,
  queryParams: {
    current: 1,
    size: 10,
  },
  total: 10,
  open: false,
  completedForm: {},
  rules: {},
});
const { queryParams, total, loading, open, completedForm, rules } =
  toRefs(data);

const tableData = ref([]);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function getList() {
  loading.value = true;
  getBorrowListFront(queryParams.value)
    .then((res) => {
      tableData.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
function handleCompleted(row) {
  proxy.$modal
    .confirm(
      '是否确认申请归还为"' +
        row.materialName +
        '"的实体书? 申请后的操作需联系管理员。'
    )
    .then(() => {
      completedForm.value = { ...row };
      completedForm.value.borrowDate = [row.borrowStartDate, row.borrowEndDate];
      open.value = true;
    });
}
function handleSubmit() {
  proxy.$refs["comForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      let params = deepClone(completedForm.value);
      if (params.abortFileList && params.abortFileList.length > 0)
        params.abortFile = params.abortFileList[0];
      pushBorrowCompleted(params)
        .then(() => {
          getList();
          ElMessage({
            message: "提交办结申请成功",
            type: "success",
            duration: 3 * 1000,
          });
          open.value = false;
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
getList();
</script>

<style scoped lang="scss">
// 懒得封装组件了，先拷贝吧
.electronic-intro {
  color: #333;
  line-height: 24px;
  font-size: 16px;

  .intro {
    margin-top: -7px;
    padding: 18px;
    background: #e3fdff;
  }
}
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }
  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
