<template>
  <div class="app-container home" v-if="!loading">
    <page-center padding="20px 0">
      <column-block title="我的收藏">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
                    <el-form-item label="资料类目：">
            <el-tree-select v-model="queryParams.materialCategory" :data="categoryOptions"
              :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择资料所属类目"
              collapse-tags :max-collapse-tags="2"
              multiple
              check-strictly style="width: 320px" />
          </el-form-item>
          <el-form-item label="馆藏分类：">
            <el-select v-model="queryParams.materialCluster" placeholder="请选择馆藏分类" clearable style="width: 200px">
              <el-option v-for="dict in electronic_cluster_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="资料名称：">
            <el-input class="handle-item" placeholder="请输入资料名称" v-model="queryParams.materialName"
              style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button type="primary" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="v-flex v-flex-wrap" v-loading="loading">
          <div class="table-item" v-for="item in tableData" :key="item.id">
            <electronic-card :data="item" style="width: 230px" :btnClick="unCollect"></electronic-card>
          </div>
          <div
            class="v-flex v-row-center collect-empty"
            v-if="tableData.length == 0"
          >
            <el-empty description="暂无收藏" style="margin: 40px 0" />
          </div>
        </div>
        <div class="pagination-box">
          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
      </column-block>
    </page-center>
  </div>
</template>

<script setup name="PortalPaperListRecord">
import { ElMessage } from "element-plus";
import useUserStore from "@/store/modules/user";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import { openFile } from "@/utils/filePreview";
import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import {
  getCategoryTree,
  getCategoryBookListFront,
} from "@/api/admin/electronics.js";
// import { cancelSubscription } from "@/api/user.js";

const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const { electronic_cluster_type, electronic_material_type } = proxy.useDict(
  "electronic_cluster_type",
  "electronic_material_type"
);
// 字典数据
const data = reactive({
  loading: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    subQueryFlag: 1,
    userId: userStore.id,
    materialCategory: undefined,
    materialName: undefined,
  },
  total: 0,
});
const categoryOptions = ref(undefined);
const urlPrefix = `${window.location.protocol}//${window.location.host}`;

const { queryParams, total, loading } = toRefs(data);
const tableData = ref([]);

// const handlePreviewClick = (item) => {
//   if (!item.materialFile?.fileUrl) {
//     ElMessageBox.alert("该资料暂无法查阅", "系统提示");
//   }
//   openFile(item.materialFile.fileUrl, item);
// };

/** 查询部门下拉树结构 */
function getTree() {
  getCategoryTree({
    categoryType: 1,
  }).then((response) => {
    categoryOptions.value = response.data;
  });
}

function getList() {
  proxy.$modal.loading();
  const newParams = { ...queryParams.value };
  newParams.materialCategoryArr=newParams.materialCategory?.join(",")||'';
  delete newParams.materialCategory;
  getCategoryBookListFront(newParams)
    .then((res) => {
      // 后端这个subscriptionFlag数据返回有问题 值为0 标识的是未收藏，导致后面组件判断出错。暂时手动修改为1
      tableData.value = res.data.records.map((item) => { item.subscriptionFlag = 1; return item });
      total.value = res.data.total;
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
function handleReset() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    subQueryFlag: 1,
    userId: userStore.id,
    materialCategory: undefined,
    materialName: undefined,
  };
  handleQuery();
}

// function deleteBook(row) {
//   proxy.$modal
//     .confirm('是否确认取消收藏"' + row.materialName + '"的图书?')
//     .then(() => {
//       proxy.$modal.loading();
//       return cancelSubscription(row.id);
//     })
//     .then(() => {
//       getList();
//       ElMessage({
//         message: "取消收藏成功",
//         type: "success",
//         duration: 3 * 1000,
//       });
//     })
//     .finally(() => {
//       proxy.$modal.closeLoading();
//     });
// }
// 取消收藏图书回调
const unCollect = (row, value) => {
  // console.log("🚀 ~ unCollect ~ row:", row, value)
  getList();
  // deleteBook(row);
};
getTree();
getList();
</script>

<style scoped lang="scss">
// 懒得封装组件了，先拷贝吧
.electronic-intro {
  color: #333;
  line-height: 24px;
  font-size: 16px;

  .intro {
    margin-top: -7px;
    padding: 18px;
    background: #e3fdff;
  }
}

.collect-warp {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10px;

  .collect-details {
    width: inherit;
    padding-right: 10px;

    .collect-name {
      font-size: 16px;
      font-weight: 600;
      flex: 1 0 auto;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 5px;

      &:hover {
        color: #409eef;
      }
    }

    .collect-date {
      font-size: 14px;
      color: #1e1e1e;
    }
  }

  .collect-btn {}
}

.collect-empty {
  width: 100%;
}

.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;

  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;
    // &:hover {
    //   transform: scale(1.2);
    // }
  }

  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}

.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
