<template>
  <div class="home">
    <page-center padding="10px 0 0">
      <column-block
        :title="`${isMine ? '我的' : ''}${isImage ? '图文' : '影音'}${
          isMine ? '' : '资料'
        }`"
      >
        <div class="image-box">
          <el-form :model="queryParams" :inline="true" label-width="80">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="查询内容:">
                  <el-input
                    class="handle-item"
                    placeholder="请输入查询内容"
                    v-model="queryParams.keyword"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资料时间" prop="year">
                  <el-date-picker
                    v-model="queryParams.year"
                    type="year"
                    placeholder="请选择资料时间"
                    value-format="YYYY"
                    style="width: 100%"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="handleQuery"
                    >筛选</el-button
                  >
                  <el-button type="primary" @click="handleReset"
                    >重置</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div v-loading="loading">
          <div style="display: flex; flex-wrap: wrap">
            <ImageCard
              v-for="(item, index) in tableList"
              :key="index"
              :data="item"
              :isImage="isImage"
              :btnClick="getList"
            ></ImageCard>
          </div>
          <div
            class="v-flex v-row-center collect-empty"
            v-if="tableList.length == 0"
          >
            <el-empty description="暂无" style="margin: 40px 0" />
          </div>
          <div class="pagination-box">
            <pagination
              v-show="total > 0"
              :total="total"
              :pageSizes="[9, 18, 27, 45]"
              v-model:page="queryParams.current"
              v-model:limit="queryParams.size"
              @pagination="getList"
            />
          </div>
        </div>
      </column-block>
    </page-center>
  </div>
</template>
<script setup name="imageOrVideo">
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import ImageCard from "@/views/portal/components/Card/ImageCard";
import { getVideoList } from "@/api/admin/video.js";
import { getImageTextListFront } from "@/api/admin/imagetext.js";
import { reactive, toRefs } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const isImage = ref(
  route.path.includes("picture") || route.query.materialTexture == 3
    ? true
    : false
);
const isMine = ref(route.path.includes("user") ? true : false);

// 监听 props 的变化来更新 ref
watch(
  () => route.path,
  () => {
    isImage.value = route.path.includes("picture") ? true : false;
    isMine.value = route.path.includes("user") ? true : false;
    getList();
  }
);
const data = reactive({
  loading: false,
  queryParams: {
    current: 1,
    size: 9,
    showSubFlag: 1,
    subQueryFlag: isMine.value ? 1 : undefined,
  },
  total: 0,
  tableList: [],
});
const { loading, queryParams, total, tableList } = toRefs(data);
function queryVideoList() {
  loading.value = true;
  getVideoList(queryParams.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 查询用户列表 */
function queryImageList() {
  loading.value = true;
  getImageTextListFront(queryParams.value)
    .then((res) => {
      tableList.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}
function handleReset() {
  queryParams.value = {
    current: 1,
    size: 9,
    showSubFlag: 1,
    subQueryFlag: isMine.value ? 1 : undefined,
    keyword: undefined,
  };
  handleQuery();
}
function getList() {
  if (isImage.value) {
    queryImageList();
  } else {
    queryVideoList();
  }
}

getList();
</script>
<style lang="scss" scoped>
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
.image-box {
  width: 99%;
  :deep(.el-form-item) {
    width: 100%;
  }
}
</style>
