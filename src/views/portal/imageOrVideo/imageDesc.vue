<template>
  <div class="app-container home">
    <page-center padding="10px 0 40px">
      <column-block title="图文详情">
        <div class="image-desc-box">
          <!-- <div class="image-desc-back">
            <el-button type="primary" @click="handleBack">返回上级</el-button>
          </div> -->
          <div class="image-desc-title">{{ imageData.materialName }}</div>
          <div class="image-desc-date">资料时间：{{ imageData.pubDate }}</div>
          <div style="width: 100%">
            <el-carousel height="479px" style="border-radius: 12px">
              <el-carousel-item
                v-for="(item, index) in imageData.materialFileList"
                :key="index"
              >
                <el-image
                  style="width: 100%; height: 100%; cursor: pointer"
                  :src="item.fileUrl"
                  fit="contain"
                />
              </el-carousel-item>
            </el-carousel>
          </div>
          <div class="image-desc-content" v-html="imageData.materialDesc"></div>
        </div>
      </column-block>
    </page-center>
  </div>
</template>
<script setup name="ImageDesc">
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import { reactive, toRefs } from "vue";
import { getImageText } from "@/api/admin/imagetext.js";
import { useRoute, useRouter } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const data = reactive({
  loading: false,
  imageData: route.query.data,
});
const { loading, imageData } = toRefs(data);

function getImageTextDesc() {
  getImageText(route.query.id).then((res) => {
    imageData.value = res.data;
  });
}

function handleBack() {
  router.go(-1);
}
onMounted(() => {
  getImageTextDesc();
});
</script>
<style lang="scss" scoped>
.image-desc-box {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 320px);
  align-items: center;
  .image-desc-back {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding: 0 40px;
  }
  .image-desc-title {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    padding: 10px 0 10px;
  }
  .image-desc-date {
    font-size: 16px;
    text-align: center;
    margin-bottom: 20px;
  }
  .image-desc-content {
    width: 1000px;
  }
}
</style>
