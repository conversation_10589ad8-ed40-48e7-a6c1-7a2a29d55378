<template>
  <div class="app-container home">
    <page-center padding="0 0 10px">
      <global-search :searchCategoryType="[3, 4]" />
    </page-center>
    <div v-loading="loading">
      <page-center padding="10px 0 20px">
        <column-block
          title="最新图文"
          :hasMore="true"
          :url="tuwen"
          :moreClick="
            () => {
              router.push({
                path: '/portal/picture',
                query: {},
              });
            }
          "
        >
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              height: 360px;
              overflow: hidden;
            "
          >
            <ImageCard
              v-for="(item, index) in imageList"
              :key="index"
              :data="item"
              :isImage="true"
              :btnClick="queryImageList"
            ></ImageCard>
          </div>
        </column-block>
      </page-center>
      <page-center padding="10px 0 40px">
        <column-block
          title="近期影音"
          :url="yingyin"
          :hasMore="true"
          :moreClick="
            () => {
              router.push({
                path: '/portal/video',
                query: {},
              });
            }
          "
        >
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              height: 360px;
              overflow: hidden;
            "
          >
            <ImageCard
              v-for="(item, index) in videoList"
              :key="index"
              :data="item"
              :isImage="false"
              :btnClick="queryVideoList"
            ></ImageCard>
          </div>
        </column-block>
      </page-center>
    </div>
  </div>
</template>
<script setup name="imageOrVideo">
import yingyin from "@/assets/images/yingyin.png";
import tuwen from "@/assets/images/tuwen.png";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import ImageCard from "@/views/portal/components/Card/ImageCard";
import { getVideoList } from "@/api/admin/video.js";
import { getImageTextList } from "@/api/admin/imagetext.js";
import { onMounted, reactive, toRefs } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const data = reactive({
  loading: false,
  queryParams: {
    current: 1,
    size: 6,
    showSubFlag: 1,
  },
  videoList: [],
  imageList: [],
});
const { loading, queryParams, videoList, imageList } = toRefs(data);
const pageNum = window.innerWidth >= 1920 ? 6 : 5;

function queryVideoList() {
  loading.value = true;
  getVideoList(queryParams.value)
    .then((res) => {
      videoList.value = res.data.records;
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 查询用户列表 */
function queryImageList() {
  loading.value = true;
  getImageTextList(queryParams.value)
    .then((res) => {
      imageList.value = res.data.records;
    })
    .finally(() => {
      loading.value = false;
    });
}
onMounted(() => {
  queryVideoList();
  queryImageList();
});
</script>
