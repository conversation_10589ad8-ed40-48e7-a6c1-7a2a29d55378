<template>
  <div class="app-container home">
    <div class="home-container">
      <page-center class="v-m-t-15">
        <global-search />
      </page-center>

      <page-center class="v-m-t-40" padding="20px 0">
        <el-row :gutter="20">
          <el-col :span="12">
            <column-block
              title="电子资料馆"
              :url="dianziziliaoguan"
              :titleTextUrl="xianshangdingyue"
              style="margin-right: 10px"
              :hasMore="false"
            >
              <div
                class="home-col-intro"
                @click="routerGo('/portal/electronic')"
              >
                <el-image
                  style="
                    width: 100%;
                    height: 320px;
                    cursor: pointer;
                    border-radius: 6px;
                  "
                  :src="dianziBanner"
                  :fit="fit"
                />
                <div class="intro">
                  <el-icon color="#114F87" size="16"><DArrowRight /></el-icon>
                  电子资料馆收录了我省历年统计制度、统计年鉴、普查资料、经济蓝皮书、统计
                  专报、统计提要、统计月报等统计资料的电子版，为用户提供便捷的在线浏览、下载及查询服务，助力高效获取权威统计信息。
                </div>
              </div>
            </column-block>
          </el-col>
          <el-col :span="12">
            <column-block
              title="实体资料馆"
              :url="shitiziliaoguan"
              :titleTextUrl="xianxiadingyue"
              style="margin-left: 10px"
              :hasMore="false"
            >
              <div class="home-col-intro" @click="routerGo('/portal/paper')">
                <el-image
                  style="
                    width: 100%;
                    height: 320px;
                    cursor: pointer;
                    border-radius: 6px;
                  "
                  :src="shitiBanner"
                />
                <div class="intro">
                  <el-icon color="#114F87" size="16"><DArrowRight /></el-icon>
                  实体图书馆收录了中国统计年鉴、外省年鉴、河南省年鉴和河南市县年鉴等多类
                  统计资料。用户可通过线上查询系统高效检索并精准定位所需资料，并登录系统
                  提交借阅申请。图书管理员将根据申请信息主动与用户取得联系，协助办理借阅手续。
                </div>
              </div>
            </column-block>
          </el-col>
        </el-row>
      </page-center>
      <!-- <page-center style="padding-bottom: 10px">
        <column-block
          title="馆藏"
          :hasMore="false"
          :url="guancang"
          class="home-electronic"
        >
          <el-row>
            <el-col :span="4" v-for="item in electronicTypeList" :key="item.id" style="text-align: center">
              <div class="home-electronic-type" @click="routerGo('/portal/search', { materialCluster: item.id })">
                <el-image style="height: 58px" :src="item.url"></el-image>
                <div class="title">{{ item.title }}</div>
              </div>
            </el-col>
          </el-row>
          <div
            style="
              display: flex;
              justify-content: center;
              width: 100%;
              margin-top: 10px;
            "
          >
            <el-row style="width: 90%">
              <el-col
                :span="6"
                v-for="item in electronicTypeList"
                :key="item.id"
                style="text-align: center"
                class="home-electronic-box"
              >
                <div
                  class="home-electronic-type"
                  @click="
                    routerGo('/portal/search', {
                      materialCluster: item.id,
                    })
                  "
                >
                  <el-image style="height: 80px" :src="item.url"></el-image>
                  <div class="title">{{ item.title }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </column-block>
      </page-center>
 -->
      <!-- <page-center style="padding-bottom: 20px">
        <column-block title="资讯信息" :hasMore="false" :url="zixun">
          <el-row style="width: 100%;">
            <el-col :span="8" style="width: 100%;">
              <div
                class="home-col-info"
                @click="routerGo('/portal/notice', { type: 1 })"
              >
                <div class="title">统计新闻</div>
                <el-image
                  style="width: 390px; height: 180px"
                  :src="tongjixinwen"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div
                class="home-col-info"
                @click="routerGo('/portal/notice', { type: 2 })"
              >
                <div class="title">最新公告</div>
                <el-image
                  style="width: 390px; height: 180px"
                  :src="zuixingonggao"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div
                class="home-col-info"
                @click="routerGo('/portal/notice', { type: 3 })"
              >
                <div class="title">专题活动</div>
                <el-image
                  style="width: 390px; height: 180px"
                  :src="zhutihuodong"
                />
              </div>
            </el-col>
          </el-row>
        </column-block>
      </page-center> -->
      <page-center padding="10px 0px 10px">
        <el-row :gutter="20">
          <el-col :span="24">
            <column-block
              title="最新电子资料"
              :url="zuixindianziziliao"
              :hasMore="true"
              :moreClick="
                () => {
                  router.push({
                    path: '/portal/electronic',
                  });
                }
              "
            >
              <div
                class="v-flex v-flex-nowrap v-row-start"
                :style="`height: 400px;padding-top: 10px;width: 1200px;`"
              >
                <div
                  class="v-flex-col v-m-x-25"
                  v-for="item in list.records"
                  :key="item.id"
                >
                  <electronic-card
                    :data="item"
                    style="width: 250px"
                  ></electronic-card>
                </div>
              </div>
            </column-block>
          </el-col>
        </el-row>
      </page-center>
      <page-center padding="10px 0px 10px">
        <el-row :gutter="20">
          <el-col :span="24">
            <column-block
              title="实体资料总览"
              :url="shitiziliaozonglan"
              :hasMore="true"
              :moreClick="
                () => {
                  router.push({
                    path: '/portal/paper',
                  });
                }
              "
            >
              <div
                class="v-flex v-flex-nowrap v-row-center"
                :style="`width: 1200px`"
              >
                <el-table :data="paperList.records" border style="width: 100%">
                  <el-table-column
                    align="center"
                    prop="materialName"
                    label="资料名称"
                  />
                  <el-table-column
                    align="center"
                    prop="categoryNames"
                    label="分类"
                  >
                    <template #default="scope">
                      <!-- {{ replaceStr(scope.row.categoryNames) }} -->
                      {{ scope.row.categoryNames }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="materialStock"
                    label="库存"
                  />
                </el-table>
              </div>
            </column-block>
          </el-col>
        </el-row>
      </page-center>
      <!-- <page-center padding="10px 0 10px">
        <el-row :gutter="20">
          <el-col :span="24">
            <column-block title="资讯动态" :url="zixun">
              <div style="width: 80%; margin: 0 auto">
                <el-carousel height="429px" style="border-radius: 5px">
                  <el-carousel-item v-for="item in pictures" :key="item.id">
                    <el-image
                      style="width: 100%; cursor: pointer"
                      :src="item.url"
                      :fit="item.fit"
                      @click="handleImageClick(item)"
                    />
                  </el-carousel-item>
                </el-carousel>
              </div>
            </column-block>
          </el-col>
        </el-row>
      </page-center> -->
    </div>
    <!-- <div class="home-container"></div> -->
  </div>
</template>

<script setup name="Index">
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import useUserStore from "@/store/modules/user";
/* import { listCarousel } from "@/api/carousel"; */
// import { tansParams } from "@/utils/ruoyi";
import { queryElectLatest } from "@/api/material";
import { queryPhysical } from "@/api/material";
import { materialType } from "@/utils/dataMap.js";
import { useMaterialCategory, useFetchlist } from "@/utils/vueUse";
import { replaceStr } from "@/utils/validate";
// 静态图片
import shitiBanner from "@/assets/images/shitiBanner.png";
import dianziziliaoguan from "@/assets/images/dianziziliaoguan.png";
import dianziBanner from "@/assets/images/dianziBanner.png";
import shitiziliaoguan from "@/assets/images/shitiziliaoguan.png";
import zixun from "@/assets/images/zixun.png";
import guancang from "@/assets/images/guancang.png";
import dianzishu from "@/assets/images/dianzishu.png";

// 馆藏·馆藏 类别图标
import ebook01 from "@/assets/images/ebook01.png";
import ebook02 from "@/assets/images/ebook02.png";
import ebook03 from "@/assets/images/ebook03.png";
import ebook04 from "@/assets/images/ebook04.png";
import ebook05 from "@/assets/images/ebook05.png";
import ebook06 from "@/assets/images/ebook06.png";
import ebook07 from "@/assets/images/ebook07.png";
import ebook08 from "@/assets/images/ebook08.png";
import xianshangdingyue from "@/assets/images/xianshangdingyue.png";
import xianxiadingyue from "@/assets/images/xianxiadingyue.png";
import zuixindianziziliao from "@/assets/images/zuixindianziziliao.png";
import shitiziliaozonglan from "@/assets/images/shitiziliaozonglan.png";

//实体资料总览
const { proxy } = getCurrentInstance();
// 馆藏·馆藏 类别图标与后端ID对应关系
const ebookTypeMap = {
  1: ebook01,
  2: ebook02,
  3: ebook03,
  4: ebook04,
  5: ebook05,
  6: ebook06,
  7: ebook07,
  8: ebook08,
};

const router = useRouter();
// console.log(shitiziliaoguan,"--shitiziliaoguan")

// const pictures = ref([]);

const electronicTypeList = ref([]);

const form = reactive({
  name: "",
  region: "",
  date1: "",
  date2: "",
  delivery: false,
  type: [],
  resource: "",
  desc: "",
});

const userStore = useUserStore();
const pageNum = window.innerWidth >= 1920 ? 6 : 5;
const { list } = useFetchlist(
  queryElectLatest,
  { materialTexture: 1, showSubFlag: userStore?.id ? 1 : 0 },
  pageNum
);
// 获取实体资料总览
const { list: paperList } = useFetchlist(
  queryPhysical,
  {
    materialTexture: 2,
  },
  20,
  function (item) {
    return {
      ...item,
      categoryClass: materialType[item.categoryClass],
    };
  }
);
console.log(paperList, "--paperList");
const { electronicCat } = useMaterialCategory();

const routerGo = (url, query) => {
  router.push({ path: url, query: query || {} });
};
// const onSubmit = (done) => {
//   console.log("submit!");
//   done();
// };

// 请求获取轮播图
/* const fetchPictures = async () => {
  const response = await listCarousel({
    subjectType: "electronic_home",
    size: 5,
  });
  pictures.value = [];
  if (response && response.code == 0) {
    const data = response.data;
    pictures.value = data.map((item) => {
      return {
        id: item.id,
        url: `${window.location.protocol}//${window.location.host}${item?.comFile?.fileUrl}`,
        fit: item?.fit || "fill",
        title: item.title,
        link: item.webUrl,
      };
    });
    // http://*************:8086/zs-hntjypt/file/2024/08/13/b477f88cdf1242d2b9b92b3488dd4b46.jpg
  }
}; */
// 电子书类型查询
/* const fetchElectronicDict = async () => {
  const response = await electronicDictData();
  if (response && response.code == 200) {
    const data = response.rows;
    const nData = [];
    data.forEach((item) => {
      nData.push({
        id: item.dictValue,
        url: ebookTypeMap[item.dictValue] || ebookTypeMap["1"],
        title: item.dictLabel,
      });
    });
    electronicTypeList.value = nData;
    // console.log(electronicTypeList, "----electronicTypeList--");
  }
}; */
/* const handleImageClick = (item) => {
  if (/^http/.test(item.link)) {
    //  return window.location.href = item.link;
    return window.open(item.link);
  }
  router.push(item.link);
}; */

/* const handelSearch = (query) => {
  const path = `/portal/search?${tansParams(query)}`;
  router.push(path);
}; */

onMounted(() => {
  /* fetchPictures(); */
  /* fetchElectronicDict(); */
});
</script>

<style scoped lang="scss">
.app-container {
  background: url("@/assets/images/bg-bottom.png") center bottom / 100% 222px
    no-repeat;
}
.home-cluster {
  border-bottom: 2px solid #0198ac;

  // .title-box {
  //   width: 120px;
  //   // height: 22px;
  //   line-height: 29px;
  //   background-color: #0198ac;
  //   text-align: center;
  //   color: #fff;
  //   font-weight: 400;
  //   font-size: 20px;
  //   font-family: SourceHanSansSC;
  // }
}

.home-col-intro {
  color: #333;
  line-height: 24px;
  font-size: 16px;

  .intro {
    margin-top: -7px;
    padding: 18px;
    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
    font-weight: 400;
    font-size: 18px;
    color: #3d3d3d;
    line-height: 25px;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    // background: #e3fdff;
  }
}

.home-col-info {
  position: relative;
  width: 390px;
  cursor: pointer;

  .title {
    position: absolute;
    left: 0;
    right: 0%;
    top: 0;
    bottom: 0;
    color: #fff;
    line-height: 190px;
    font-size: 46px;
    font-weight: 800;
    color: #ffffff;
    text-shadow: 0px 2px 4px rgba(4, 15, 75, 0.5);
    z-index: 1;
    text-align: center;
  }
}
.home-electronic {
  background-image: url("@/assets/images/ebook-bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  padding-bottom: 200px;
}
.home-electronic-box {
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5),
  &:nth-child(7) {
    background-color: #ffffff;
    font-weight: 800;
    font-size: 18px;
    color: #1c63e0;
  }
  &:nth-child(1),
  &:nth-child(3),
  &:nth-child(6),
  &:nth-child(8) {
    background: linear-gradient(180deg, #3c9cf2 0%, #1c63e0 100%);
    font-weight: 800;
    font-size: 18px;
    color: #ffffff;
  }
  .home-electronic-type {
    width: 100%;
    aspect-ratio: 16 / 9;
    cursor: pointer;
    padding: 50px 0 40px;
    display: inline-block;

    .title {
      padding-top: 5%;
      line-height: 27px;
    }
  }
}
</style>
