<template>
  <div class="app-container home" v-if="!loading">
    <page-center class="v-m-t-15">
      <global-search />
    </page-center>
    <page-center class="v-m-t-40">
      <el-row>
        <el-col :span="24">
          <column-block
            title="电子资料馆"
            :url="dianziziliaoguan"
            :hasMore="true"
            :moreClick="
              () => {
                router.push('/portal/electronic/list');
              }
            "
          >
            <div class="electronic-intro">
              <div class="v-m-r-10">
                <el-image
                  style="width: 600px; height: 300px; border-radius: 6px"
                  :src="dianziBanner"
                  fit="fill"
                />
              </div>
              <div class="intro v-flex v-col-top">
                <el-icon class="v-m-t-5 v-m-r-10" color="#114F87" size="16"
                  ><DArrowRight
                /></el-icon>
                <div>
                  <div class="v-font-22" style="line-height: 32px">
                    电子资料馆收录了我省历年统计制度、统计年鉴、普查资料、经济蓝皮书、
                    统计专报、统计提要、统计月报等统计资料的电子版，为用户提供便捷的在线浏览、下载及查询服务，助力高效获取权威统计信息。
                  </div>
                  <!-- <div class="v-m-b-10">
                    <span style="font-weight: 600"
                      >本馆对全国、地方的主要统计年鉴（资料）进行了数字化处理形成PDF电子资料总计{{
                        totalCount
                      }}。
                    </span>
                    其中：
                  </div>
                  <div v-for="pItem in electronicCat" :key="pItem.id">
                    {{ pItem.label }}分为：<span
                      v-for="(item, index) in pItem.children"
                      :key="item.id"
                      >{{ index ? "、" : "" }}{{ item.label }}</span
                    >；
                    共计：{{ pItem.showCount || 0 }}本
                  </div> -->
                </div>
              </div>
            </div>
          </column-block>
        </el-col>
      </el-row>
    </page-center>
    <page-center padding="10px 0">
      <el-row :gutter="20">
        <el-col :span="24">
          <column-block :url="guancangfenlei" title="电子馆藏分类">
            <el-row :gutter="20" style="width: inherit; margin: 0 auto">
              <el-col
                :span="6"
                v-for="(pItem, index) in electronicCat"
                :key="pItem.id"
              >
                <!-- <div
                  :class="{}"
                  class="label-entrce v-pointer"
                  @click="handleSearch(pItem)"
                >
                  {{ pItem.label }}
                </div> -->

                <div
                  :class="[
                    'label-entrce',
                    Math.floor(index / 4) % 2 === 0 // 判断行号是偶数还是奇数
                      ? `label-entrce${index % 2}` // 偶数行：蓝红蓝红
                      : `label-entrce${(index + 1) % 2}`, // 奇数行：红蓝红蓝
                  ]"
                  class="v-pointer"
                  @click="handleSearch(pItem)"
                >
                  {{ pItem.label }}
                </div>
              </el-col>
            </el-row>
          </column-block>
        </el-col>
      </el-row>
    </page-center>
    <page-center padding="10px 0 40px">
      <el-row :gutter="20">
        <!-- <el-col :span="7">
          <div class="grid-content ep-bg-purple">
            <topic-card cardType="electronic"></topic-card>
          </div>
        </el-col> -->
        <el-col :span="24">
          <column-block
            title="最新图书"
            :hasMore="true"
            :url="zuixindianziziliao"
            :moreClick="
              () => {
                router.push({
                  path: '/portal/search',
                  query: {
                    materialCategory: electronicCat[0].id,
                    materialTexture: 1,
                  },
                });
              }
            "
          >
            <div
              class="v-flex v-flex-nowrap v-row-around"
              style="height: 400px; padding-top: 10px"
            >
              <div
                class="v-flex-col v-m-x-25"
                v-for="item in list.records"
                :key="item.id"
              >
                <electronic-card
                  :data="item"
                  style="width: 250px"
                ></electronic-card>
              </div>
            </div>
          </column-block>
        </el-col>
      </el-row>
    </page-center>
    <!-- <page-center padding="10px 0 40px">
      <el-row :gutter="20">
        <el-col :span="24">
          <column-block title="热点排行">
            <div style="height: 298px; overflow-y: auto">
              <el-table
                :data="hotList.records"
                :stripe="true"
                :border="true"
                :show-header="false"
                style="width: 100%"
              >
                <el-table-column
                  align="center"
                  prop="date"
                  label="表头"
                  width="100"
                >
                  <template #default="scope">
                    <span>TOP{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  prop="materialName"
                  label="数名"
                  width="280"
                />
                <el-table-column align="center" label="">
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      size="small"
                      @click="handlePreviewClick(scope.row)"
                      >查看</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </column-block>
        </el-col>
      </el-row>
    </page-center> -->
  </div>
</template>

<script setup name="Electronic">
import dianziBanner from "@/assets/images/dianziBanner.png";
import dianziziliaoguan from "@/assets/images/dianziziliaoguan.png";
import guancangfenlei from "@/assets/images/guancangfenlei.png";
// import guancangItem0 from "@/assets/images/guancang-item0.png";
// import guancangItem1 from "@/assets/images/guancang-item1.png";
// import guancangItem2 from "@/assets/images/guancang-item2.png";
import TopicCard from "@/views/portal/components/Card/TopicCard";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import { openFile } from "@/utils/filePreview";
import useUserStore from "@/store/modules/user";
import { queryElectLatest } from "@/api/material";
import zuixindianziziliao from "@/assets/images/zuixindianziziliao.png";
import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import {
  electronicSetting,
  electronicSettingQuery,
  electronicHotRank,
  electronicIntro,
} from "@/api/electronicHome";
import { useMaterialCategory, useFetchlist } from "@/utils/vueUse";
import { computed } from "vue";
const userStore = useUserStore();
// 根据屏幕尺寸获取对应数量书籍
const pageNum = window.innerWidth >= 1920 ? 6 : 5;
const { list } = useFetchlist(
  queryElectLatest,
  { materialTexture: 1, showSubFlag: userStore?.id ? 1 : 0 },
  pageNum
);
const { list: hotList } = useFetchlist(electronicHotRank, {}, 7);
const { electronicCat } = useMaterialCategory();
console.log("🚀 ~ electronicCat:", electronicCat);

const { proxy } = getCurrentInstance();
const router = useRouter();
const totalCount = computed(() => {
  let total = 0;
  if (!electronicCat?.value) return "-";
  electronicCat?.value.map((item) => {
    total = total + (parseInt(item.showCount) || 0);
  });
  return total;
});
const handlePreviewClick = (item) => {
  if (!item.materialFile?.fileUrl) {
    return proxy.$modal.msgWarning("该资料暂无法查阅");
  }
  openFile(item.materialFile.fileUrl, item);
};

//
const data = reactive({
  queryParams: {
    current: 1,
    size: 5,
    name: "电子资料馆",
  },
  //简介数据
  introduction: {
    totalCount: 0,
    bookCount: 0,
    materialCount: 0,
  },
  loading: false,
});

const fetchElectronicQuery = async () => {
  const response = await electronicSetting({});
  if (response.code == 0 && response.data) {
    data.introduction = response.data;
    data.introduction.totalCount =
      response.data.bookCount + response.data.materialCount;
  }
  // pictures.value = response.data;
};
const handleSearch = (data) => {
  router.push({
    path: "/portal/search",
    query: { materialCategory: data.id, materialTexture: 1 },
  });
};

onMounted(() => {
  // fetchPictures();
  fetchElectronicQuery();
  // fetchHotRank();
});
</script>

<style scoped lang="scss">
// 懒得封装组件了，先拷贝吧
.electronic-intro {
  color: #333;
  line-height: 24px;
  font-size: 16px;
  display: flex;
  width: 100%;

  .intro {
    flex: 1 0 0;
    overflow-y: scroll;
    padding: 18px;
    // background: #e3fdff;
    height: 300px;
    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
    font-weight: 400;
    font-size: 18px;
    color: #3d3d3d;
    line-height: 25px;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.app-container {
  background: url("@/assets/images/bg-bottom.png") center bottom / 100% 222px
    no-repeat;

  .page-center {
    overflow: unset;
  }
  .label-entrce {
    // width: 100%;
    width: 287px;
    height: 118px;
    font-size: 24px;
    text-align: center;
    // background: linear-gradient(180deg, #5dd5ff 0%, #1d8ad4 100%);
    // border-radius: 8px;
    font-weight: 600;
    margin: 10px 0;
    // padding: 20px;
    font-weight: 800;
    font-size: 22px;
    color: #ffffff;
    line-height: 118px;
    text-shadow: 0px 2px 2px rgba(12, 19, 104, 0.5);
    overflow: hidden;
    // min-height: 73px;
  }

  .label-entrce0 {
    background: url("@/assets/images/guancang-item0.png") center / 100% 100%
      no-repeat;
  }
  .label-entrce1 {
    background: url("@/assets/images/guancang-item2.png") center / 100% 100%
      no-repeat;
  }
  .label-entrce2 {
    background: url("@/assets/images/guancang-item2.png") center / 100% 100%
      no-repeat;
  }
}
</style>
