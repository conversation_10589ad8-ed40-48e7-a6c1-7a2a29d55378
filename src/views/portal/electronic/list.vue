<template>
  <div class="app-container home">
    <!-- <page-center padding="30px 0">
      <global-search />
    </page-center> -->
    <page-center padding="30px 0">
      <column-block title="电子资料馆">
        <el-form :inline="true" :model="form" class="demo-form-inline">
          <el-form-item label="资料类别：">
            <!-- <el-select v-model="form.materialCategoryParent" placeholder="请选择材料质地" style="width: 210px" clearable
              @change="changeParent">
              <el-option v-for="item in electronicCat" :key="item.id" :label="item.label" :value="item.id" />
            </el-select> -->
            <el-tree-select
              v-model="form.materialCategoryTree"
              collapse-tags
              :max-collapse-tags="2"
              :props="{ value: 'id', label: 'label', children: 'children' }"
              value-key="id"
              :data="electronicCat"
              multiple
              :render-after-expand="false"
              style="width: 320px"
            />
          </el-form-item>
          <!-- <el-form-item label="资料类别：">
            <el-select v-model="form.materialCategoryArr" multiple collapse-tags placeholder="请选择资料类别"
              style="width: 210px">
              <el-option v-for="item in getElectronicChildrenById(form.materialCategoryParent)" :key="item.id"
                :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="资料名称：">
            <el-input
              v-model="form.materialName"
              placeholder="请输入要查询的内容"
              clearable
              style="width: 210px"
              @keyup.enter="onSubmit"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <div>
          <div v-if="list.records.length === 0" class="table-no-data">
            <el-empty description="暂无数据" style="margin: 40px 0" />
          </div>
          <div v-else class="table-box">
            <div class="table-item" v-for="item in list.records" :key="item.id">
              <electronic-card
                :data="item"
                class="v-m-x-5"
                style="width: 250px"
              ></electronic-card>
            </div>
          </div>
          <div class="pagination-box">
            <pagination
              v-show="list.total > 0"
              :total="list.total"
              v-model:current="list.current"
              v-model:size="list.size"
              @pagination="pageChange"
            />
          </div>
        </div>
      </column-block>
    </page-center>
  </div>
</template>

<script setup name="PortalElectronicList">
import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";

import { materialPage } from "@/api/material";
import useUserStore from "@/store/modules/user";

import { useFetchlist, useMaterialCategory } from "@/utils/vueuse";
// import { toRaw } from "vue";
const treeProps = { lable: "id" };
const { electronicCat, getElectronicChildrenById } = useMaterialCategory();

const userStore = useUserStore();
const { list, pageChange, queryList } = useFetchlist(
  materialPage,
  {
    showSubFlag: userStore?.id ? 1 : 0,
    materialTexture: 1,
  },
  10
);
const form = reactive({
  materialTexture: 1,
  materialCategoryArr: [], // 质地分类
  materialCategoryTree: null,
  materialCategoryParent: "", // 电子资料类目
  materialName: "", // 关键词
});

const onSubmit = () => {
  const params = {
    ...toRaw(form),
    materialCategoryArr: form.materialCategoryTree.join(","),
  };
  console.log(params, "params", form);
  queryList(params);
};
// const onPageChange= (page) => {
//   pageChange(page?.current||1,page?.size);
// };
// const data = reactive({
//   selectParentOptions: [],
//   selectChildrenOptions: [],
//   loading: false,
//   tableData: [],
//   queryParams: {
//     current: 1,
//     size: 10,
//     materialName: "",
//   },
//   total: 0,
// });
// const {
//   selectParentOptions,
//   selectChildrenOptions,
//   loading,
//   tableData,
//   queryParams,
//   total,
// } = toRefs(data);

// const getListCategory = async () => {
//   const params = {
//     current: 1,
//     size: 100,
//     parentId: 0,
//     categoryType: 1,
//   };
//   const { data } = await QueryListCategory(params);
//   selectParentOptions.value = data;
//   getListTable();
// };

// const changeParent = async () => {
//   const params = {
//     current: 1,
//     size: 100,
//     parentId: form.materialCategoryParent,
//     categoryType: 1,
//   };
//   const { data } = await QueryListCategory(params);
//   selectChildrenOptions.value = data;
// };

// const getListTable = async () => {
//   const params = {
//     ...queryParams.value,

//     materialCategoryArr: form.materialCategoryArr.length
//       ? form.materialCategoryArr.join(",")
//       : form.materialCategoryParent,
//     categoryType: 1,
//   };

//   loading.value = true;
//   const response = await materialPage(params);
//   tableData.value = [];
//   total.value = [];
//   if (response && response.code == 0) {
//     tableData.value = response.data.records;
//     total.value = response.data.total;
//   }
//   loading.value = false;
// };

// const curElectronicCat = computed(() => {
//   return getCategoryChildrenById(form.materialCategoryParent) || [];
// });

watch(
  () => form.materialCategoryParent,
  (newValue) => {
    form.materialCategoryArr = [];
  }
);
</script>

<style scoped lang="scss">
.table-no-data {
  color: #999;
  text-align: center;
  height: 100%;
  font-size: 18px;
  padding: 30px;
}

// 懒得封装组件了，先拷贝吧
// .electronic-intro {
//   color: #333;
//   line-height: 24px;
//   font-size: 16px;

//   .intro {
//     margin-top: -7px;
//     padding: 18px;
//     background: #e3fdff;
//   }
// }
.table-box {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: start;
}

.table-item {
  padding: 15px 20px;
}

.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
