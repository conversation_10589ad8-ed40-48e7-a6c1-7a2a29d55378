<template>
  <div class="main">
    <div class="v-flex v-col-top v-row-center content-top">
      <div class="v-flex-col v-row-center v-col-center content-input">
        <div
          class="v-flex v-row-top v-col-center v-p-x-300 v-p-y-15 v-m-b-40 v-pointer back-home"
          @click="handleBack"
        >
          <i class="el-icon-back"></i>
          返回首页
        </div>
        <div class="title-info">
          <dict-tag
            :options="sys_notice_type"
            :value="queryParams.noticeType"
          />
        </div>
        <div class="v-flex-base v-m-t-24 search-input">
          <el-input
            v-model.trim="queryParams.noticeTitle"
            class="input"
            placeholder="请输入搜索内容"
          />
          <el-button class="search" type="primary" @click="handleQuery"
            >搜索</el-button
          >
        </div>
      </div>
    </div>
    <div class="v-flex-base v-m-x-300 v-m-y-15 content-center">
      <div
        class="v-r-4 right-main"
        v-if="rightContentArr && rightContentArr.length > 0"
      >
        <div
          class="v-flex-base v-pointer v-m-t-5 v-p-x-20 v-p-y-20 right-content"
          v-for="(item, index) in rightContentArr"
          :key="index"
          @click="handleDetails(item)"
        >
          <div class="v-flex-1 v-m-l-15 right">
            <div class="title" :title="item.noticeTitle">
              {{ item.noticeTitle }}
            </div>
            <div class="title-list" :title="item.memo">{{ item.memo }}</div>
            <div class="title-bottom">
              <div class="title-bottom-left">
                <!-- <div style="margin-right: 12px">{{ item.source }}</div> -->
                <div>{{ item.createTime }}</div>
              </div>
              <div class="title-bottom-right">
                <!-- <span style="margin-right: 4px">阅读</span>
                {{ item.viewNum || 0 }} -->
              </div>
            </div>
          </div>
        </div>
        <div class="pagination-style">
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
      <div class="right-empty" v-if="rightContentArr.length == 0">
        <el-empty description="暂无数据" style="margin: 40px 0" />
      </div>
    </div>
  </div>
</template>
<script setup name="notice">
import { listNotice } from "@/api/system/notice";
const { proxy } = getCurrentInstance();
const { sys_notice_type } = proxy.useDict("sys_notice_type");
const router = useRouter();
const route = useRoute();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    noticeTitle: undefined,
  },
  total: 0,
});
const { queryParams, total } = toRefs(data);
const rightContentArr = ref([]);

function handleBack() {
  router.push("/portal/index");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
function handleDetails(item) {
  router.push({
    path: "/portal/noticeDetails",
    query: { id: item.noticeId },
  });
}
/** 查询公告列表 */
function getList() {
  listNotice(queryParams.value).then((response) => {
    rightContentArr.value = response.rows;
    total.value = response.total;
  });
}
onMounted(() => {
  queryParams.value.noticeType = route.query.type;
  getList();
});
</script>
<style lang="scss" scoped>
.main {
  .content-top {
    width: 100%;
    height: 270px;
    background-image: url("@/assets/images/newbg.png");
    background-size: 100% 100%;
    .content-input {
      text-align: center;
      width: 100%;
      .back-home {
        width: 100vw;
        color: #ffffff;
        font-weight: 600;
        &:hover {
          color: #dededf;
        }
        &:active {
          color: #cbcccd;
        }
      }

      .title-info {
        width: 400px;
        margin: auto;
        font-size: 56px;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 40px;
      }

      .search-input {
        width: 554px;
        height: 44px;
        line-height: 44px;
        position: relative;

        .input {
          width: 100%;
          outline: none;
          background: rgba(255, 255, 255, 0.3);
          opacity: 1;
          border-radius: 35px;
          overflow: hidden;

          &::placeholder {
            padding-left: 10px;
            font-size: 18px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
            line-height: 22px;
          }
        }

        .search {
          position: absolute;
          right: 5px;
          top: 5px;
          width: 84px;
          height: 34px;
          line-height: 34px;
          background: #ffffff;
          border-radius: 29px 29px 29px 29px;
          opacity: 1;
          font-size: 18px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          color: #0357ca;
          :deep(.el-input__inner) {
            padding: 10px;
          }
        }
      }
    }
  }
  .content-center {
    .right-main {
      width: 100%;
      padding: 10px 20px;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.8);
      opacity: 1;

      .right-content {
        border-bottom: 1px solid #e8e8e8;

        .left {
          width: 173px;
          height: 108px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .right {
          width: calc(100% - 190px);

          .title {
            font-size: 16px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 700;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .title-list {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin: 12px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }

          .title-bottom {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;

            .title-bottom-left {
              display: flex;
            }

            .title-bottom-right {
              float: right;
            }
          }
        }
      }

      .pagination-style {
        margin-top: 50px;
        text-align: center;
        :deep(.pagination-container .el-pagination) {
          position: relative;
        }
        :deep(.pagination-container) {
          height: 100%;
        }
      }
    }
    .right-empty {
      margin: 0 auto;
    }

    .right-main::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
