<template>
  <div class="content-container">
    <div class="breadcrumb-nav">
      <span class="breadcrumb-back" @click="handleBack">
        <i class="el-icon-back"></i>
        返回
      </span>
      <span>当前位置：</span>
      <router-link :to="`/portal/notice?type=${model.noticeType}`">
        <dict-tag
          style="display: inline"
          :options="sys_notice_type"
          :value="model.noticeType"
        />
      </router-link>
    </div>
    <div class="article">
      <div class="info-summary">
        <div class="title-wrapper title">
          {{ model.noticeTitle }}
        </div>
        <div class="desc-info">
          <span>时间：{{ model.createTime }}</span>
        </div>
      </div>
      <div class="detail">
        <div ref="content" class="content" v-html="model.noticeContent" />
      </div>
      <div v-if="fileList && fileList.length > 0" class="attachment">
        <div class="file-name">附件：</div>
        <div v-for="(file, index) in fileList" :key="file.id">
          <a class="attachment-item" @click="downLoadFile(file)">
            <span>{{ index + 1 }}、{{ file.fileName }}</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup name="noticeSSDetails">
import { getNotice } from "@/api/system/notice";

const { proxy } = getCurrentInstance();
const { sys_notice_type } = proxy.useDict("sys_notice_type");
const router = useRouter();
const route = useRoute();
const data = reactive({
  model: {},
});
const { model } = toRefs(data);
function getContent() {
  getNotice(route.query.id).then((res) => {
    model.value = res.data;
  });
}
function handleBack() {
  router.go(-1);
}
getContent();
</script>
<style lang="scss" scoped>
.content-container {
  padding-bottom: 24px;
}
.content {
  padding-bottom: 24px;
}

.breadcrumb-nav {
  width: 1200px;
  margin: 0 auto;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #666666;
  padding: 20px 0;
  .breadcrumb-back {
    margin-right: 20px;
    cursor: pointer;
    &:hover {
      color: #0357ca;
    }
    &:active {
      color: #0357ca;
    }
  }
}

.article {
  width: 1200px;
  margin: 0 auto;
  background: #fff;
  padding: 20px;

  .info-summary {
    margin-top: 20px;

    .title-wrapper {
      font-weight: bold;
      font-size: 26px;
      text-align: center;
      color: #333333;
      margin: 20px 0;
    }
  }
}

.detail {
  padding: 20px;

  ::v-deep img {
    max-width: 100%;
  }
}

.attachment-item {
  display: block;
  margin-bottom: 10px;
  height: 19px;
  font-size: 16px;
  font-weight: 400;
  color: #0357ca;
  line-height: 22px;
  cursor: pointer;

  &:hover {
    color: rgb(28, 106, 255);
  }
}

.attachment {
  padding: 20px;

  .file-name {
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 400;
    color: #313131;
    line-height: 25px;
  }
}

.desc-info {
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #666666;

  & span:not(:first-child) {
    margin-left: 37px;
  }

  & span:last-child {
    span {
      cursor: pointer;
      margin: 0;

      &:not(:first-child) {
        margin-left: 10px;
      }
    }
  }
}
</style>
