<template>
  <div class="app-container home" v-if="!loading">
    <page-center padding="30px 0">
      <el-row>
        <el-col :span="6">
          <column-block title="专题类别">
            <div style="height: 800px">
              <el-menu
                default-active="2"
                :collapse="isCollapse"
                class="topic-menu"
              >
                <el-menu-item
                  v-for="(item, index) in categoryArray"
                  :index="index"
                  :key="item.id"
                  @click="handleSelect(item, index)"
                  >{{ item.subjectName }}</el-menu-item
                >
              </el-menu>
            </div>
          </column-block>
        </el-col>
        <el-col :span="18">
          <column-block
            :title="curCategory ? curCategory.name : '专题'"
            style="margin-left: 10px"
          >
            <div style="height: 800px">
              <div class="topic-intro" v-if="curCategory">
                <el-image
                  style="width: 300px; height: 180px"
                  :src="curCategory.imgUrl"
                  :fit="fit"
                />
                <div class="intro">
                  {{ curCategory.intro }}
                </div>
              </div>
              <div>
                <el-table :data="list.records" style="width: 100%" al>
                  <el-table-column
                    align="center"
                    prop="index"
                    label="序号"
                    width="90"
                  />
                  <el-table-column
                    align="center"
                    prop="subjectMaterialName"
                    label="名称"
                  />
                  <el-table-column
                    align="center"
                    prop="createTime"
                    label="入库时间"
                  />
                  <el-table-column align="center" label="操作" width="150">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="handlePreviewClick(scope.row)"
                      >
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="pagination-box">
                  <!-- <pagination
                    :page-size="list.size"
                    :total="list.total"
                    :current-page="list.current"
                    @current-change="pageChange"
                  /> -->

                  <pagination
                    v-show="list.total > 0"
                    :total="list.total"
                    v-model:page="list.current"
                    v-model:limit="list.size"
                    @pagination="pageChange"
                  />
                </div>
              </div>
            </div>
          </column-block>
        </el-col>
      </el-row>
    </page-center>
  </div>
</template>

<script setup name="PortalElectronicList">
// import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import ElectronicCard from "@/views/portal/components/Card/ElectronicCard";
import { openFile } from "@/utils/filePreview";
import { topicQuery, topicCategoryQuery } from "@/api/topic";
import { useFetchlist } from "@/utils/vueuse";
import { useRouter } from "vue-router";
import { watch } from "vue";
// import { compile, computed, toRaw } from "vue";
// import { reactify } from "@vueuse/core";
const route = useRoute();
const router = useRouter();

//当前选择专题类目索引
const curCategoryId = ref(parseInt(route.query?.id) || null);

const form = reactive({
  subjectCategoryId: route.query?.id || "", //专题类目id
  subjectMaterialName: "", //专题资料名称
  subjectMaterialType: route.params?.type == "electronic" ? 1 : 2, //专题资料类型 1-电子资料 2-实体图书
});
const { proxy } = getCurrentInstance();
const { list, pageChange, queryList } = useFetchlist(
  topicQuery,
  toRaw(form),
  20
);
// 专题类目查询列表 下拉框数据
const categoryArray = ref([]);
const curCategory = computed(() => {
  if (!categoryArray.value.length) return null;
  const cur = categoryArray.value.find((item) => {
    if (item.id == curCategoryId.value) return true;
  });
  if (!cur) return null;
  return {
    imgUrl: cur.comFile?.fileUrl,
    intro: cur.subjectIntro,
    name: cur.subjectName,
    id: cur.id,
  };
});

const handleSelect = (item, index) => {
  // console.log("🚀 ~ handleSelect ~ router.path:", route.path)
  // :route="`/portal/topic/${topicType}?categoryId=${item.id}`"
  router.push({ path: route.path, query: { id: item.id } });
};
const handlePreviewClick = (item) => {
  if (!item.comFile?.fileUrl) {
    return proxy.$modal.msgWarning("该资料暂无法查阅");
  }
  openFile(item.comFile?.fileUrl, item);
};
// 获取专题类目
const fetchTopicCategory = async () => {
  const response = await topicCategoryQuery({
    subjectType: form.subjectMaterialType,
  });
  if (response && response.code == 0) {
    categoryArray.value = response.data;
    if (response.data[0]?.id) {
      curCategoryId.value = response.data[0].id;
    }
  }
};
onMounted(() => {
  fetchTopicCategory();
});
watch(
  route,
  async (val) => {
    const topicTypeId = route.params?.type == "electronic" ? 1 : 2;
    form.subjectMaterialType = topicTypeId;
    curCategoryId.value = parseInt(route.query?.id) || null;
    queryList({
      subjectCategoryId: route.query?.id || "", //专题类目id
      subjectMaterialType: topicTypeId, //专题资料类型 1-电子资料 2-实体图书
    });
  }
  // { deep: true, immediate: true }
);
</script>

<style scoped lang="scss">
.table-no-data {
  color: #999;
  text-align: center;
  height: 100%;
  font-size: 18px;
  padding: 30px;
}

.table-box {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: start;
}

.table-item {
  padding: 20px 30px;
}

.topic-menu {
  border-color: transparent;
}

.topic-intro {
  padding: 10px 0;
  display: flex;
  flex-direction: row;

  .intro {
    padding: 18px;
  }
}

// .pagination-box {
//     display: flex;
//     align-items: end;
//     justify-content: flex-end;
//     padding: 20px;
// }
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
