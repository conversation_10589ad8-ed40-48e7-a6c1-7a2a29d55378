<template>
  <div class="home">
    <page-center>
      <global-search
        ref="queryRef"
        @handelSearch="
          (query) => {
            fetchTable(query);
          }
        "
      />
    </page-center>
    <page-center padding="20px 0">
      <column-block title="筛选结果" :hasMore="false" v-loading="loading">
        <el-table
          :data="list.records"
          border
          style="width: 100%"
          :default-sort="{ prop: 'materialName', order: 'ascending' }"
          @sort-change="sortChange"
        >
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="150"
          />
          <el-table-column
            align="center"
            prop="materialName"
            label="名称"
            sortable
            min-width="220"
          />
          <el-table-column align="center" label="质地" prop="materialTexture">
            <template #default="scope">
              <dict-tag
                :options="material_class_search"
                :value="scope.row.materialTexture"
              />
            </template>
          </el-table-column>

          <!--  <el-table-column align="center" label="质地分类" prop="materialType">
            <template #default="scope">
              <dict-tag
                :options="material_type_search"
                :value="scope.row.materialType"
              />
            </template>
          </el-table-column> -->

          <el-table-column
            prop="materialCategoryName"
            label="分类 —— 类目 —— 子类"
            align="center"
            width="280"
          >
            <template #default="scope">
              {{ replaceStr(scope.row.materialCategoryName) }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="materialYear"
            label="资料年份"
            width="120"
            sortable
          />
          <el-table-column
            align="center"
            prop="materialStock"
            label="库存"
            width="120"
          />

          <!-- <el-table-column
            label="馆藏分类"
            align="center"
            prop="materialCluster"
          >
            <template #default="scope">
              <dict-tag
                :options="electronic_cluster_type"
                :value="scope.row.materialCluster"
              />
            </template>
          </el-table-column> -->

          <el-table-column align="center" label="操作" width="140">
            <template #default="scope">
              <el-button
                v-if="scope.row.materialTexture == 1"
                link
                type="primary"
                size="small"
                @click="handlePreviewClick(scope.row)"
              >
                查看
              </el-button>

              <el-button
                link
                :loading="scope.row.loading"
                type="primary"
                size="small"
                v-if="scope.row.materialTexture == 1"
                @click="downloadFile(scope.row.materialFile, scope.row.loading)"
              >
                下载
              </el-button>

              <record-or-collect-btn
                :materialTexture="scope.row.materialTexture"
                :option="scope.row"
                :click="handleClick"
              />
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-box">
          <pagination
            v-show="list.total > 0"
            :total="list.total"
            v-model:current="list.current"
            v-model:size="list.size"
            @pagination="pageChange"
          />
        </div>
      </column-block>
    </page-center>
    <dialog-record
      ref="refRecordBook"
      :visible="dialogOpt.visible"
      :dialogForm="dialogOpt.item"
    />
  </div>
</template>

<script setup name="searchResult">
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import GlobalSearch from "@/views/portal/components/Search/GlobalSearch";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import RecordOrCollectBtn from "@/views/portal/components/RecordOrCollectBtn/RecordOrCollectBtn";
import DialogRecord from "@/views/portal/components/DialogBtn/DialogRecord";
import { reactive, ref } from "vue";
import { materialPage } from "@/api/material";
import { openFile } from "@/utils/filePreview";
import { useFetchlist } from "@/utils/vueuse";
import useUserStore from "@/store/modules/user";
import { replaceStr } from "@/utils/validate";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const { material_class_search, material_type_search, electronic_cluster_type } =
  proxy.useDict(
    "material_class_search",
    "material_type_search",
    "electronic_cluster_type"
  );
const formQuery = reactive({
  orderBy: "materialName", //排序字段 materialName materialYear
  sortType: "ASC", // 排序方式 ASC 升序  DESC 降序
});
const { list, loading, pageChange, queryList } = useFetchlist(
  materialPage,
  Object.assign(
    {
      showSubFlag: userStore?.id ? 1 : 0,
      orderBy: formQuery.orderBy,
      sortType: formQuery.sortType,
    },
    route.query || {}
  ),
  20
);
// 弹框处理对象
const dialogOpt = reactive({
  visible: false,
  item: {},
});
// const { loading, tableData, queryParams, total } = toRefs(data);
//
const sortChange = (header) => {
  formQuery.orderBy = header.prop;
  if (header.order == "ascending") {
    formQuery.sortType = "ASC";
  } else {
    formQuery.sortType = "DESC";
  }
  console.log(handleObj.value);
  /* loading.value = true; */
  // queryList(handleObj.value);
};
const handlePreviewClick = (item) => {
  // console.log("click", item);
  if (!item.materialFile?.fileUrl) {
    return proxy.$modal.msgWarning("该资料暂无法查阅");
  }
  openFile(item.materialFile.fileUrl, item);
};
const handleClick = (item, isElectronic) => {
  if (isElectronic) return;
  dialogOpt.item = item;
  dialogOpt.visible = true;
  proxy.$refs.refRecordBook.showDialog(item);
};
const fetchTable = async (params) => {
  console.log(params);
  /* const routeQuery = route.query || {};
  loading.value = true;
  const response = await materialPage({
    current: params.current || 1,
    size: params.size || 20,
    materialCluster: routeQuery.cluster,
    ...params,
  });

  tableData.value = [];
  total.value = [];
  if (response && response.code == 0) {
    tableData.value = response.data.records;
    total.value = response.data.total;
  }
  loading.value = false; */
};
// 下载文件
const downloadFile = (data, loading) => {
  const url = data.fileUrl.replace(/\\/g, "/");
  const xhr = new XMLHttpRequest();
  xhr.open("GET", url, true);
  xhr.responseType = "blob";
  //xhr.setRequestHeader('Authorization', 'Basic a2VybWl0Omtlcm1pdA==');
  loading = true;
  xhr.onload = () => {
    if (xhr.status === 200) {
      // 获取文件blob数据并保存
      saveAs(xhr.response, data.fileName);
    }
    loading = false;
  };
  xhr.send();
};
const handleObj = ref();
watch(
  () => router.currentRoute.value,
  (newValue) => {
    const query = Object.assign({}, newValue.query);
    Object.keys(query).forEach((key) => key || delete query[key]);
    console.log("🚀 ~ query:", query);
    // for
    // console.log("🚀 ~ query:", query)
    // delete query[''];
    // // queryParams.value = {
    // //   ...newValue.query,
    // // };
    handleObj.value = query;
    queryList(query);
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
