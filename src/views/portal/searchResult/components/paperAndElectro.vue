<template>
  <page-center padding="10px 0">
    <el-row>
      <el-col :span="6">
        <!-- :title="materialCategoryName" -->
        <column-block :showTitle="false">
          <div style="height: calc(100vh - 280px); overflow: auto">
            <el-tree
              ref="categoryTree"
              :data="categoryArrTree"
              node-key="id"
              :props="{
                label: 'label',
                children: 'children',
              }"
              highlight-current
              @node-click="handleTree"
              class="custom-tree"
              default-expand-all
            ></el-tree>
            <!-- <el-menu
              :default-active="curCategory?.id"
              :collapse="isCollapse"
              class="topic-menu"
            >
              <el-menu-item
                v-for="(item, index) in categoryArray"
                :index="item.id"
                :key="item.id"
                style="
                  font-size: 16px;
                  font-weight: 500;
                  color: #2e90df;
                  background-color: rgba(0, 0, 0, 0.02);
                  margin-bottom: 10px;
                "
                @click="handleSelect(item, index)"
                >{{ item.categoryName }}</el-menu-item
              >
            </el-menu> -->
          </div>
        </column-block>
      </el-col>
      <el-col :span="18">
        <column-block
          :title="
            childrenNodeName ||
            curCategoryName ||
            curCategory.categoryName ||
            '类目'
          "
          class="v-m-l-10"
        >
          <div style="height: calc(100vh - 280px); overflow: auto">
            <div>
              <el-form :model="form" ref="queryRef" :inline="true">
                <el-form-item label="资料名称" prop="materialName">
                  <el-input
                    v-model="form.materialName"
                    placeholder="请输入资料名称"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="资料年份">
                  <el-date-picker
                    v-model="form.materialYear"
                    value-format="YYYY"
                    type="year"
                    style="width: 200px"
                    placeholder="资料年份"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="searchHandel"
                    >搜索</el-button
                  >
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-form>

              <el-table
                :data="list.records"
                border
                size="large"
                @sort-change="sortChange"
                style="width: 100%; height: calc(100vh - 425px); overflow: auto"
              >
                <el-table-column
                  align="center"
                  prop="materialName"
                  label="资料名称"
                  sortable
                />
                <el-table-column
                  align="center"
                  prop="materialYear"
                  label="资料年份"
                  width="210"
                  v-if="route.query.materialTexture == 1"
                  sortable
                />
                <el-table-column
                  align="center"
                  prop="materialNo"
                  v-else
                  label="资料编号"
                  sortable
                />
                <el-table-column align="center" label="操作" width="210">
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      size="small"
                      v-if="scope.row.materialTexture == 1"
                      @click="handlePreviewClick(scope.row)"
                    >
                      查看
                    </el-button>

                    <el-button
                      link
                      :loading="scope.row.loading"
                      type="primary"
                      size="small"
                      v-if="scope.row.materialTexture == 1"
                      @click="
                        downloadFile(
                          scope.row.materialFile,
                          scope.row.loading,
                          scope.row.loginFlag
                        )
                      "
                    >
                      下载
                    </el-button>

                    <record-or-collect-btn
                      :materialTexture="scope.row.materialTexture"
                      :option="scope.row"
                      :click="handleCollectRecord"
                    />
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-box">
                <pagination
                  v-show="list.total > 0"
                  :total="list.total"
                  v-model:page="list.current"
                  v-model:limit="list.size"
                  @pagination="pageChange"
                />
              </div>
            </div>
          </div>
        </column-block>
      </el-col>
    </el-row>
    <dialog-record ref="refRecordBook" />
  </page-center>
</template>

<script setup name="PortalElectronicList">
import { ElMessageBox } from "element-plus";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import RecordOrCollectBtn from "@/views/portal/components/RecordOrCollectBtn/RecordOrCollectBtn";
import DialogRecord from "@/views/portal/components/DialogBtn/DialogRecord";
import { openFile } from "@/utils/filePreview";
import { QueryListCategory } from "@/api/category";
import { materialPage } from "@/api/material";
import { getCategoryTree } from "@/api/admin/electronics.js";
import { useFetchlist } from "@/utils/vueuse";
import { useRouter } from "vue-router";
import { watch } from "vue";
import useUserStore from "@/store/modules/user";
import { addIncreaseClick } from "@/api/material";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const emit = defineEmits();
const form = reactive({
  materialTexture: route.query.materialTexture,
  materialCategoryArr: route.query.childrenId || route.query.materialCategory,
  showSubFlag: userStore?.id ? 1 : 0,
  materialCluster: route.query.materialCluster,
  materialYear: "",
  materialName: route.query.materialName || "",
  orderBy: "materialName", //排序字段 materialName materialYear
  sortType: "ASC", // 排序方式 ASC 升序  DESC 降序
});

const props = defineProps({
  materialCategoryName: {
    type: String,
    default: "",
  },
  childrenNodeName: {
    type: String,
    default: "",
  },
});

const { list, pageChange, queryList } = useFetchlist(
  materialPage,
  {
    ...toRaw(form),
    materialTexture: route.query.materialTexture,
    materialCategoryArr:
      route.query.childrenId ||
      route.query.materialCategory ||
      route.query.category,
    showSubFlag: userStore?.id ? 1 : 0,
    materialCluster: route.query.materialCluster,
  },
  20
);

// 专题类目查询列表 下拉框数据
const categoryArray = ref([]);
const curCategory = computed(async () => {
  let currentObj =
    categoryArray.value.find((item) => item.id == route.query.childrenId) || {};
  return currentObj;
});

/* const handleSelect = (item) => {
  router.push({
    path: route.path,
    query: { ...route.query, childrenId: item.id },
  });
}; */

const searchHandel = (item) => {
  queryList({
    ...toRaw(form),
    materialTexture: route.query.materialTexture,
    materialCategoryArr:
      route.query.childrenId ||
      route.query.materialCategory ||
      route.query.category,
    showSubFlag: userStore?.id ? 1 : 0,
    materialCluster: route.query.materialCluster,
  });
};
const resetQuery = () => {
  form.materialYear = "";
  form.materialName = "";
  queryList({
    ...toRaw(form),
  });
};
const handlePreviewClick = (item) => {
  if (!userStore.id && item.loginFlag == 1) {
    ElMessageBox.confirm("您还未登录，是否前往登录？", "提示", {
      confirmButtonText: "登录",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const loginUrl = `${
          import.meta.env.VITE_APP_SSO_LOGIN_URL
        }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
        if (import.meta.env.MODE == "production") {
          window.open(loginUrl, "_self");
        } else {
          router.push("/login");
        }
      })
      .catch(() => {
        // 用户选择取消，不做任何操作
      });
    return;
  }

  if (!item.materialFile?.fileUrl) {
    return proxy.$modal.msgWarning("该资料暂无法查阅");
  } else {
    openFile(item.materialFile?.fileUrl, item);
    // addIncreaseClick(item.id).then((res) => {
    //   openFile(item.materialFile?.fileUrl, item);
    // });
  }
};

/*************  ✨ Codeium Command ⭐  *************/
/**
 *  Handling collection and record operations for physical and electronic documents
 * @param {Object} item - The document object
 */
/******  06bfc888-99df-48f8-9e90-ad281c6b3d6f  *******/ const handleCollectRecord =
  (item) => {
    // if (!userStore.id) {
    //   ElMessageBox.confirm("您还未登录，是否前往登录？", "提示", {
    //     confirmButtonText: "登录",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(() => {
    //       router.push({ path: "/login" });
    //     })
    //     .catch(() => {
    //       // 用户选择取消，不做任何操作
    //     });
    //   return;
    // }

    if (item.materialTexture == 2) {
      proxy.$refs.refRecordBook.showDialog(item);
    }
    queryList({
      ...toRaw(form),
      materialTexture: route.query.materialTexture,
      materialCategoryArr:
        route.query.childrenId ||
        route.query.materialCategory ||
        route.query.category,
      showSubFlag: userStore?.id ? 1 : 0,
      materialCluster: route.query.materialCluster,
    });
  };
const sortChange = (header) => {
  form.orderBy = header.prop;
  if (header.order == "ascending") {
    form.sortType = "ASC";
  } else {
    form.sortType = "DESC";
  }
  searchHandel();
};
/* // 获取专题类目
const fetchTopicCategory = async () => {
  const params = {
    current: 1,
    size: 100,
    parentId: route.query.materialCategory,
    categoryType: route.query.materialTexture,
  };
  const { data } = await QueryListCategory(params);
  categoryArray.value = data;
}; */

const categoryArrTree = ref([]);
const curCategoryName = ref("");
// 获取专题类目-树结构
const fetchCategoryTree = async () => {
  const { data } = await getCategoryTree({
    categoryType: route.query.materialTexture,
  });
  if (route.query.materialCategory) {
    categoryArrTree.value = data.filter(
      (item) => item.id == route.query.materialCategory
    );
  } else {
    categoryArrTree.value = data;
  }

  // 高亮节点
  nextTick(() => {
    const tree = proxy.$refs.categoryTree;
    if (tree) {
      const expandParentNodes = (node) => {
        if (node.parent) {
          console.log("node.parent", tree);
          tree.expandNode(node.parent, true);
          expandParentNodes(node.parent);
        }
      };
      const materialCategory =
        route.query.materialCategory || route.query.category;
      const targetNode = tree.getNode(materialCategory);
      if (targetNode) {
        tree.setCurrentKey(materialCategory);
        expandParentNodes(targetNode);
      }
    }
  });
};

const handleTree = (obj) => {
  console.log("-----treeData-----", obj);
  if (obj.children && obj.children.length) return;
  curCategoryName.value = obj.label;
  router.push({
    path: route.path,
    query: { ...route.query, childrenId: obj.id },
  });
};

// 下载文件
const downloadFile = (data, loading, loginFlag) => {
  if (!userStore.id && loginFlag == 1) {
    ElMessageBox.confirm("您还未登录，是否前往登录？", "提示", {
      confirmButtonText: "登录",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        // router.push({ path: "/login" });
        const loginUrl = `${
          import.meta.env.VITE_APP_SSO_LOGIN_URL
        }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
        if (import.meta.env.MODE == "production") {
          window.open(loginUrl, "_self");
        } else {
          router.push("/login");
        }
      })
      .catch(() => {
        // 用户选择取消，不做任何操作
      });
    return;
  }

  const url = data.fileUrl.replace(/\\/g, "/");
  const xhr = new XMLHttpRequest();
  xhr.open("GET", url, true);
  xhr.responseType = "blob";
  //xhr.setRequestHeader('Authorization', 'Basic a2VybWl0Omtlcm1pdA==');
  loading = true;
  xhr.onload = () => {
    if (xhr.status === 200) {
      // 获取文件blob数据并保存
      saveAs(xhr.response, data.fileName);
    }
    loading = false;
  };
  xhr.send();
};
onMounted(() => {
  fetchCategoryTree();
  // fetchTopicCategory();
});
watch(
  route,
  async () => {
    form.materialYear = "";
    form.materialName = "";
    queryList({
      ...toRaw(form),
      materialTexture: route.query.materialTexture,
      materialCategoryArr:
        route.query.childrenId || route.query.materialCategory,
      showSubFlag: userStore?.id ? 1 : 0,
      materialCluster: route.query.materialCluster,
    });
  },
  { deep: true }
);
</script>

<style scoped lang="scss">
.table-no-data {
  color: #999;
  text-align: center;
  height: 100%;
  font-size: 18px;
  padding: 30px;
}

.table-box {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: start;
}

.table-item {
  padding: 20px 30px;
}

.topic-menu {
  border-color: transparent;
}

.topic-intro {
  padding: 10px 0;
  display: flex;
  flex-direction: row;

  .intro {
    padding: 18px;
  }
}
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-menu-item.is-active) {
  background-color: rgba(0, 0, 0, 0.06) !important;
}

:deep(.el-tree.custom-tree) {
  .el-tree-node {
    .el-tree-node__content {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      font-weight: 500;
    }
    &.is-current:not(.is-expanded) {
      .el-tree-node__content {
        color: #2e90df;
        background: #f5f4f4;
      }
    }
  }
}
</style>
