<template>
  <div class="app-container home">
    <keyWordSearch
      v-if="route.query.materialName || route.query.materialCluster"
    />
    <div v-else>
      <page-center v-if="breadcrumbArr.length">
        <div class="v-flex v-m-l-10">
          <span style="color: #909399">当前位置：</span>
          <el-breadcrumb separator=">" style="color: #909399">
            <el-breadcrumb-item
              v-for="item in breadcrumbArr"
              class="v-flex"
              :to="{ path: item.path, query: item.query }"
              >{{ item.label }}</el-breadcrumb-item
            >
          </el-breadcrumb>
        </div>
      </page-center>
      <!-- 实体资料和电子资料高级搜索结果页面 -->
      <paperAndElectro
        v-if="['1', '2'].includes(route.query.materialTexture)"
        :materialCategoryName="materialCategoryName"
        :childrenNodeName="childrenNodeName"
      ></paperAndElectro>

      <mediaResult
        v-if="['3', '4'].includes(route.query.materialTexture)"
      ></mediaResult>
    </div>
  </div>
</template>

<script setup name="searchResult">
import paperAndElectro from "./components/paperAndElectro";
import keyWordSearch from "./components/keyWordSearch";
import PageCenter from "@/views/portal/components/PageCenter/PageCenter";
import mediaResult from "@/views/portal/imageOrVideo/image.vue";
import { queryCategoryNameByIds } from "@/api/material";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const breadcrumbArr = ref([]);
const materialCategoryName = ref("");
const childrenNodeName = ref("");

watch(
  () => router.currentRoute.value,
  async () => {
    const materialTextureKey = {
      1: { path: "/portal/electronic", label: "电子资料馆" },
      2: { path: "/portal/paper", label: "实体资料馆" },
      3: { path: "/portal/picture", label: "图文资料馆" },
      4: { path: "/portal/video", label: "影音资料馆" },
    };

    if (!route.query.materialTexture) {
      return;
    }

    breadcrumbArr.value = [
      {
        path: "/",
        label: "首页",
      },
      {
        path: materialTextureKey[route.query.materialTexture]["path"],
        label: materialTextureKey[route.query.materialTexture]["label"],
      },
    ];

    if (route.query.materialCategory) {
      let { data } = await queryCategoryNameByIds(route.query.materialCategory);
      breadcrumbArr.value.push({
        path: route.path,
        label: data,
        query: {
          materialTexture: route.query.materialTexture,
          materialCategory: route.query.materialCategory,
        },
      });
      materialCategoryName.value = data;
      childrenNodeName.value = data;
    }

    if (route.query.childrenId) {
      let { data } = await queryCategoryNameByIds(route.query.childrenId);
      breadcrumbArr.value.push({
        path: route.path,
        label: data,
        query: {
          materialTexture: route.query.materialTexture,
          materialCategory: route.query.materialCategory,
          childrenId: route.query.childrenId,
        },
      });
      childrenNodeName.value = data;

      console.log(
        breadcrumbArr.value,
        route.path,
        materialCategoryName.value,
        childrenNodeName.value
      );
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.pagination-box {
  :deep(.pagination-container .el-pagination) {
    position: relative;
  }

  :deep(.pagination-container) {
    height: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-breadcrumb__inner.is-link) {
  cursor: pointer;
  color: #909399;
  font-size: 16px;
}
</style>
