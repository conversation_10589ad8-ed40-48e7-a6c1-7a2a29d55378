<template>
  <el-dialog title="申请借阅" v-model="visible" width="700" append-to-body>
    <el-form
      ref="dialogFormRef"
      :model="dialogForm"
      :rules="dialogFormRules"
      label-width="auto"
    >
      <el-form-item label="名称：">
        <span class="inblock-title">{{ dialogForm.materialName }}</span>
        <el-form-item label="资料年份：">
          <span>{{ dialogForm.materialYear }}</span>
        </el-form-item>
      </el-form-item>
      <el-form-item label="库存：">
        <span class="inblock-title">{{ dialogForm.curStock }}本</span>
        <el-form-item label="借阅数量：">
          <span>1 本</span>
        </el-form-item>
      </el-form-item>
      <el-form-item label="借阅时间：" prop="time">
        <el-date-picker
          v-model="dialogForm.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD"
          :disabled-date="handleDisabledDate"
        />
      </el-form-item>
      <el-form-item label="借阅说明：" prop="borrowExplain">
        <el-input v-model="dialogForm.borrowExplain" type="textarea" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button
          type="primary"
          :loading="loading"
          @click="submitForm(dialogForm)"
          >确认</el-button
        >
        <el-button @click="visible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="LendForm">
import { physicalApply } from "@/api/material";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const data = reactive({
  dialogForm: {
    time: [],
  },
  visible: false,
  loading: false,
  dialogFormRef: null,
});

const { visible, dialogForm, loading, dialogFormRef } = toRefs(data);

// 借阅时间禁用日期
const thirtyDays = 60 * 24 * 3600 * 1000; // 60天的毫秒数
const handleDisabledDate = (time) => {
  const now = Date.now();
  // 设定一个时间范围，例如不允许选择超过30天的日期
  return time.getTime() > now + thirtyDays || time.getTime() < now;
};

const dialogFormRules = reactive({
  time: [{ required: true, message: "请选择借阅时间", trigger: "change" }],
  borrowExplain: [
    { required: true, message: "请填写借阅说明", trigger: "blur" },
  ],
});

async function showDialog(row) {
  resetForm();
  dialogForm.value = row;
  visible.value = true;
}
// 表单重置
const resetForm = () => {
  dialogForm.value = {
    time: [],
  };
};

async function submitForm(dialogForm) {
  proxy.$refs["dialogFormRef"].validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const response = await physicalApply({
        materialId: dialogForm.id,
        borrowNum: 1,
        borrowExplain: dialogForm.borrowExplain,
        borrowStartDate: dialogForm.time[0],
        borrowEndDate: dialogForm.time[1],
      });

      if (response.code == 0) {
        proxy.$modal.msgSuccess("您的借阅已提交", "借阅提醒");
        visible.value = false;
        emits("getList");
      } else {
        ElMessage.error(response.msg);
      }
      loading.value = false;
    } else {
      proxy.$modal.msgWarning("请核对表单内容！");
    }
  });
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
