<template>
  <slot name="btn" :showDialog="showDialog"></slot>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="700"
    top="30vh"
    modal-class="self-dialog"
  >
    <slot name="content"></slot>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="showCancelBtn" @click="closeDilog">取消</el-button>
        <el-button type="primary" @click="submit">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="DialogBtn">
const dialogVisible = ref(false);

const props = defineProps({
  title: {
    type: String,
    default: "分类检索",
  },
  showCancelBtn: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["submit"]);
const showDialog = () => {
  dialogVisible.value = true;
};
const closeDilog = () => {
  dialogVisible.value = false;
};

const submit = () => {
  emit("submit", () => {
    dialogVisible.value = false;
  });
};

// provide('dialogBtn', {
//     showDialog,
//     closeDilog
// })
</script>

<style scoped lang="scss">
.self-dialog {
  border-color: rgb(7, 106, 112);
  .el-dialog {
    border: 1px solid rgb(7, 106, 112);
  }
}

:v-deep(.el-form-item--default .el-form-item__label) {
  font-size: 16px !important;
}

:v-deep(.el-cascader-node__label) {
  font-size: 16px !important;
}
</style>
