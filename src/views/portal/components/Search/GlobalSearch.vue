<template>
  <div class="search-global-box">
    <dialog-btn @submit="onSubmit">
      <template #btn="slotProps">
        <!-- <el-button
          type="primary"
          @click="dialogHandel(slotProps)"
          color="#2E90DF"
        >
          <el-icon :size="18">
            <Filter />
          </el-icon>

          <template #icon>
            <span class="search-icon-filter"></span>
          </template>

          <span style="padding-left: 5px">分类检索</span>
        </el-button> -->

        <div class="filter-btn" @click="dialogHandel(slotProps)">
          <span class="search-icon-filter"></span>
          <span style="padding-left: 5px">分类检索</span>
        </div>
      </template>
      <template #content>
        <div>
          <el-form
            :model="queryParams"
            label-width="auto"
            style="max-width: 640px; padding: 10px 20px; font-size: 16px"
          >
            <el-form-item
              size="large"
              style="font-size: 16px"
              label="资料名称："
            >
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入资料名称或部分关键词"
              ></el-input>
            </el-form-item>
            <el-form-item size="large" label="资料质地：">
              <el-cascader
                style="width: 100%"
                @change="sortChange"
                v-model="queryParams.materialTexture"
                :options="treeList"
                :props="classifyProps"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>
    </dialog-btn>
    <el-input
      class="mid-input"
      v-model="queryParams.materialName"
      @keyup.enter.native="onSubmit(() => {}, true)"
      placeholder="请选择您要寻找的资料熟悉或者直接输入要检索的信息"
    ></el-input>
    <!-- <el-button
      type="primary"
      color="#2E90DF"
      @keyup.enter.native="onSubmit(() => {}, true)"
      @click="onSubmit(() => {}, true)"
      >搜ㅤ索</el-button
    > -->
    <div
      class="search-button"
      @keyup.enter.native="onSubmit(() => {}, true)"
      @click="onSubmit(() => {}, true)"
    >
      搜索
    </div>
  </div>
</template>

<script setup name="GlobalSearch">
import DialogBtn from "@/views/portal/components/DialogBtn/DialogBtn";
import { getCategoryTree } from "@/api/admin/electronics.js";

import { tansParams } from "@/utils/ruoyi";
import { onMounted, toRefs } from "vue";
import { getDicts } from "@/api/system/dict/data";
/* const { proxy } = getCurrentInstance();
const { material_class_search, material_type_search } = proxy.useDict(
  "material_class_search",
  "material_type_search"
); */

const props = defineProps({
  searchCategoryType: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["handelSearch"]);
const router = useRouter();
const route = useRoute();
const classifyProps = {
  expandTrigger: "hover",
  value: "id",
  checkStrictly: true,
};
const data = reactive({
  queryParams: {
    materialTexture: [],
    category: "", // 资料类目
    materialName: "", // 关键词
  },
  radioOptions: [], // 资料类目option
});

const { queryParams, radioOptions } = toRefs(data);
const treeList = ref([]);
// 获取质地分类数据
const getListCategory = async (categoryType) => {
  const { data } = await getCategoryTree({
    categoryType, //类目类型  1-电子资料  2-实体图书
  });
  if (categoryType == 1) {
    treeList.value[0].children = data;
  } else {
    treeList.value[1].children = data;
  }
};
const onSubmit = (done, resetQuery) => {
  console.log(queryParams.value.materialTexture);
  done && done();

  if (resetQuery) {
    queryParams.value.category = "";
  }
  const materialCategory = queryParams.value.category;
  const query = {
    materialCategory: materialCategory,
    materialTexture: queryParams.value.materialTexture
      ? queryParams.value.materialTexture[0]
      : "",
    materialName: queryParams.value.materialName,
    category: queryParams.value.materialTexture
      ? queryParams.value.materialTexture[
          queryParams.value.materialTexture.length - 1
        ].toString()
      : "",
  };
  if (props.searchCategoryType?.length > 0) {
    query.materialTextureStr = props.searchCategoryType.join(",");
  }
  console.log(query);
  let queryStr = tansParams(query);
  const path = `/portal/search?${tansParams(query)}`;
  if (queryStr) {
    router.push(path);
  }
};
// 打开弹框
//TODO 影音图文路由确定之后再改枚举
const dialogHandel = (slot) => {
  slot.showDialog();
};
const sortChange = (e) => {
  console.log(e);
};
onMounted(() => {
  const materialTextureKey = {
    "/portal/electronic": "1",
    "/portal/paper": "2",
    "/portal/picture": "3",
    "/portal/video": "4",
  };
  queryParams.value = {
    materialTexture:
      materialTextureKey[route.path] || route.query.materialTexture,
    category: route.query.materialCategory, // 质地分类
  };
  treeList.value = [];
  getDicts("material_class_search").then((res) => {
    res.data.forEach((item) => {
      treeList.value.push({
        label: item.dictLabel,
        id: item.dictValue,
        children: [],
      });
    });
    getListCategory(1);
    getListCategory(2);
  });
});
</script>

<style scoped lang="scss">
.search-global-box {
  // border: 1px solid #0198ac;
  font-family: PingFangSC, PingFang SC;
  border-radius: 14px;
  background-color: #fff;
  // padding: 0px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: #f5faff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #114f87;
  height: 70px;

  .el-button {
    line-height: 46px;
    height: 46px;
    padding: 12px 30px;
    --el-border-radius-base: 14px;
    --el-button-size: 46px;
    --el-font-size-base: 16px;
  }
  .mid-input {
    --el-input-hover-border-color: transparent;
    --el-input-clear-hover-color: transparent;
    --el-input-focus-border-color: transparent;
    --el-input-border-color: transparent;
    --el-input-height: 46px;
    font-size: 16px;
  }
}

.borderless .el-input__inner {
  border: none;
  box-shadow: none;
}

.radio-item {
  width: 120px;
}

:v-deep(.el-radio__label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mid-input {
  :deep(.el-input__wrapper) {
    background-color: #f5faff !important;
    font-size: 18px;
    color: #979797;
    line-height: 26px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}

.filter-btn {
  width: 173px;
  text-align: center;
  line-height: 70px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 18px;
  color: #114f87;
  text-align: center;
  font-style: normal;
  text-transform: none;
  border-right: 1px solid #114f87;
  cursor: pointer;
}

.search-icon-filter {
  margin-left: 5px;
  display: inline-block;
  width: 24px;
  height: 24px;
  position: relative;
  top: 5px;
  background: url("@/assets/images/fenleijiansuo.png") center / 100% 100%
    no-repeat;
}

.search-button {
  width: 157px;
  height: 70px;
  background: #114f87;
  border-radius: 0px 8px 8px 0px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  line-height: 71px;
  letter-spacing: 12px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  text-align: center;
  cursor: pointer;
  padding-left: 10px;
}

/* el-form 中 label 的字体大小设置为 16px */
:v-deep(.el-form-item .el-form-item__label) {
  font-size: 16px !important;
}

/* el-cascader 面板中所有文字的字体大小设置为 16px */
:v-deep(.el-cascader-panel .el-cascader-node__label) {
  font-size: 16px !important;
}

:v-deep(.el-cascader-menu .el-cascader-node__label) {
  font-size: 16px !important;
}

:v-deep(.el-cascader__dropdown .el-cascader-node__label) {
  font-size: 16px !important;
}
</style>
