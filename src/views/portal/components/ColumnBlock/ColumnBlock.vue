<template>
  <div class="column-block" :style="style">
    <div
      class="cblock-title-box"
      :style="{
        background: `url(${url}) left center / 61px 48px no-repeat`,
      }"
      v-if="showTitle"
    >
      <div class="cblock-title" :style="{ marginLeft: url ? '50px' : '0' }">
        <!-- <el-image
          v-if="url"
          style="width: 24px; height: 24px; margin-right: 8px"
          :src="url"
          fit="contain"
        /> -->
        {{ title }}
      </div>
      <div v-if="hasMore" class="cblock-more-box" @click="moreClick">
        <span>更多</span>
        <el-icon><ArrowRight /></el-icon>
      </div>
      <div
        v-if="titleTextUrl"
        :style="{
          background: `url(${titleTextUrl}) center / 100% 100% no-repeat`,
        }"
        class="title-text"
      ></div>
    </div>
    <div class="cblock-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup name="ColumnBlock">
import { ref } from "vue";
const props = defineProps({
  showTitle: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "",
  },
  hasMore: {
    type: Boolean,
    default: false,
  },
  moreClick: {
    type: Function,
    default: () => {},
  },
  style: {
    type: String,
    defalut: "",
  },
  url: {
    type: String,
    defalut: "",
  },
  titleTextUrl: {
    type: String,
    defalut: "",
  },
});
</script>
<style scoped lang="scss">
.column-block {
  // width: 1000px;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: auto;
  background: #fff;
  border-radius: 0px;
}

.cblock-title-box {
  height: 52px;
  border-bottom: 1px solid #d8d8d8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  .cblock-title {
    // width: 120px;
    // height: 22px;
    line-height: 30px;
    // background-color: #0198ac;
    text-align: center;
    color: #666666;
    font-weight: 900;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: SourceHanSansSC;
    // margin-left: 50px;
  }
}

.cblock-more {
  font-size: 14px;
  color: #0198ac;
  cursor: pointer;
  font-weight: 400;
  // font-family: SourceHanSansSC;
}

.cblock-content {
  width: 100%;
  // height: 100%;
  box-sizing: border-box;
  overflow: auto;
  // padding: 20px 0;
  padding-top: 11px;
}
.cblock-more-box {
  /* font-family: "Courier New", Courier, monospace; */
  color: #114f87;
  font-size: 18px;
  font-weight: 800;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.title-text {
  width: 106px;
  height: 18px;
  position: relative;
  top: 5px;
  right: -15px;
}
</style>
