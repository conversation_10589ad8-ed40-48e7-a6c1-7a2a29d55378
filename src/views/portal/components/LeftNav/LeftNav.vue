<template>
    <div class="left-nav-box">
        <el-radio-group>
            <el-radio-button :value="false">expand</el-radio-button>
            <el-radio-button :value="true">collapse</el-radio-button>
        </el-radio-group>
        <el-menu default-active="2" class="el-menu-vertical-demo" :collapse="isCollapse" @open="handleOpen"
            @close="handleClose">
            <el-menu-item index="2">类型</el-menu-item>
            <el-menu-item index="2">类型</el-menu-item>
            <el-menu-item index="2">类型</el-menu-item>
        </el-menu>
    </div>
</template>

<script setup name="LeftNav">

const props = defineProps({
    cls: {
        type: String,
        default: ''
    },
    padding: {
        type: String,
        default: '0'
    }
});
</script>
<style scoped lang="scss">
.left-nav-box {
    background-color: #fff;
    margin-right: 10px;
}
</style>