<template>
    <div>
        <span>{{ value }}</span>
    </div>
</template>

<script setup name="CategoryTag">
// import { useMaterialCategory } from "@/utils/vueuse";

// const { category, electronicCat } = useMaterialCategory();

const props = defineProps({
    // 数据
    options: {
        type: Array,
        default: [],
    },
    // 当前的值
    value: [Number, String, Array],
    // 当未找到匹配的数据时，显示value
    showValue: {
        type: Boolean,
        default: '-',
    },
});

const value = computed(() => {
    if (props.value === null || typeof props.value === 'undefined' || props.value === '') return [];
    // let resLabel;
    const material = props.options.material;
    const electronicCat =  props.options.electronic;
    let found;
    // const rowCategory = toRaw(category);
    if (material.length) {
        found = material.find((item) => (item.id === props.value))
    }
    if (!found && electronicCat.length) {
        for (let index = 0; index < electronicCat.length; index++) {
            const electItem = electronicCat[index].children;
            if (found) break;
            found = electItem.find((item) => (item.id === props.value))
        }
    }

    return found?.label || props.showValue;
});

</script>

<style scoped></style>