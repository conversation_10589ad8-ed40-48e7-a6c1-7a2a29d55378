<template>
  <template v-if="isElectronic">
    <el-button
      class="button-collect"
      :link="btnType == 'table' ? true : false"
      :size="btnType == 'table' ? 'small' : 'default'"
      :class="btnType == 'table' ? '' : 'ebook-card-collect'"
      :type="isCollected ? 'success' : 'primary'"
      @click="handleCollectClick"
    >
      {{ isCollected ? "已定阅" : "订阅" }}
    </el-button>
  </template>
  <template v-else>
    <el-button link type="primary" size="small" @click="handleRecordClick">
      借阅
    </el-button>
  </template>
</template>

<script setup name="RecordOrCollectBtn">
import { ElMessageBox, ElMessage } from "element-plus";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
import { subscription } from "@/api/electronicHome";
import { cancelSubscription } from "@/api/user";
const router = useRouter();
const props = defineProps({
  // 质地 1-电子资料  2-实体资料
  materialTexture: {
    type: Number,
    default: 1,
  },
  btnType: {
    type: String,
    default: "table", // or  card
  },
  option: {
    type: Object,
    default: {},
  },
  click: {
    type: Function,
    default() {
      return () => {};
    },
  },
});
const isElectronic = computed(() =>
  [1, 3, 4].includes(props.materialTexture) ? true : false
);
const isCollected = ref(props.option.subscriptionFlag == 1 ? true : false);
// 监听 props 的变化来更新 ref
watch(
  () => props.option.subscriptionFlag,
  (newValue) => {
    isCollected.value = newValue == 1 ? true : false;
  }
);

// 点击借阅处理函数
const handleRecordClick = () => {
  const item = props.option;
  if (!item.materialStock && !item.realStock) {
    return proxy.$modal.msgWarning("库存不足，无法借阅");
  }
  if (!userStore.id || (item.needLogin && !userStore.id)) {
    return ElMessageBox.confirm(
      "请登录，发起借书，管理员跟您联系借出事宜",
      "系统提示",
      {
        confirmButtonText: "登录",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).then(() => {
      // location.href = import.meta.env.VITE_APP_LOGIN_PATH;
      const loginUrl = `${
        import.meta.env.VITE_APP_SSO_LOGIN_URL
      }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
      if (import.meta.env.MODE == "production") {
        window.open(loginUrl, "_self");
      } else {
        router.push("/login");
      }
    });
  }
  props.click && props.click(props.option, isElectronic.value);
};
// 点击订阅处理函数
const handleCollectClick = () => {
  if (userStore.id) {
    return isCollected.value ? fetchUnCollect() : fetchCollect();
  }
  ElMessageBox.confirm("请登录，订阅内容可在我的订阅中查看", "系统提示", {
    confirmButtonText: "登录",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // location.href = import.meta.env.VITE_APP_LOGIN_PATH;
    const loginUrl = `${
      import.meta.env.VITE_APP_SSO_LOGIN_URL
    }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
    if (import.meta.env.MODE == "production") {
      window.open(loginUrl, "_self");
    } else {
      router.push("/login");
    }
  });
  props.click && props.click(props.option, isElectronic.value);
};
// 订阅请求发起函数
const fetchCollect = async () => {
  const res = await subscription(props.option.id);
  if (res.code == 0) {
    proxy.$message.success({
      message: "订阅成功",
      type: "success",
    });
    // 需要调整卡片状态
    isCollected.value = true;
    // cardData.value.subscriptionFlag = 1
  } else {
    proxy.$message.error(res.error);
  }
  props.click && props.click(props.option, isElectronic.value);
};
const fetchUnCollect = async () => {
  proxy.$modal
    .confirm('是否确认取消订阅"' + props.option.materialName + '"的图书?')
    .then(() => {
      proxy.$modal.loading();
      return cancelSubscription(props.option.id);
    })
    .then(() => {
      ElMessage({
        message: "取消订阅成功",
        type: "success",
        duration: 3 * 1000,
      });
      isCollected.value = false;
    })
    .finally(() => {
      proxy.$modal.closeLoading();
      props.click && props.click(props.option, isElectronic.value);
    });
};
</script>
<style scoped lang="scss"></style>
