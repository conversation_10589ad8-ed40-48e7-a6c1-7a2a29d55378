<template>
  <div :class="['page-center', cls]" :style="{ padding }">
    <slot></slot>
  </div>
</template>

<script setup name="PageCenter">
import { ref } from "vue";
const props = defineProps({
  cls: {
    type: String,
    default: "",
  },
  padding: {
    type: String,
    default: "0",
  },
  isChildrenComponent: {
    type: Boolean,
    default: false,
  },
});
</script>
<style scoped lang="scss">
@import "@/assets/styles/variables.module.scss";
.page-center {
  min-width: $base-page-container-width;
  margin: 0 auto;
  box-sizing: border-box;
  // overflow: auto;
}

@media screen and (min-width: 1440px) {
  .page-center {
    width: $base-page-container-width;
  }
}

@media screen and (min-width: 1920px) {
  .page-center {
    width: $base-page-container-width;
  }
}
</style>
