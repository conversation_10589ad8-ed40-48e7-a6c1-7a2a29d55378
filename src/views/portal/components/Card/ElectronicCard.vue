<template>
  <div class="ebook-card" :style="style" :title="cardData.materialName">
    <div class="ebook-card-img">
      <div class="ebook-card-img-def" v-if="!cardData.icon?.fileUrl">
        <el-image
          style="width: 100%; height: 300px"
          fit="cover"
          :src="defaultCover"
          :title="cardData.materialName"
          @click="handlePreview"
        >
        </el-image>
        <span class="title">{{ cardData.materialName }}</span>
      </div>
      <el-image
        v-else
        style="width: 100%; height: 300px"
        fit="cover"
        :src="cardData.icon?.fileUrl"
        :title="cardData.materialName"
        @click="handlePreview"
      ></el-image>
    </div>
    <div class="ebook-card-content">
      <!-- <div class="ebook-card-title">
        <div class="ebook-card-name">{{ cardData.materialName }}</div>
        <div class="ebook-card-time">{{ time }}</div>
      </div> -->
      <record-or-collect-btn
        :materialTexture="1"
        :option="cardData"
        btnType="card"
        :click="btnClick"
      />
    </div>
  </div>
</template>

<script setup name="ElectronicCard">
import { subscription } from "@/api/electronicHome";
import { addIncreaseClick } from "@/api/material";
import { openFile } from "@/utils/filePreview";
import useUserStore from "@/store/modules/user";
import RecordOrCollectBtn from "@/views/portal/components/RecordOrCollectBtn/RecordOrCollectBtn";
import { ElMessageBox } from "element-plus";
import defaultCover from "@/assets/images/defaultCover.jpg";
const userStore = useUserStore();
// subscriptionFlag    订阅标识 1-已订阅 0-未订阅
const { proxy } = getCurrentInstance();
// const obj={type:"primary"}
// :fileUrl="item.materialFile?.fileUrl" :imgUrl="item?.icon?.fileUrl" :title="item.materialName" :id="item.id" :time="item.pubDate"
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  style: {
    type: String,
    default: "",
  },
  btnClick: {
    type: Function,
    default: () => {},
  },
});

const { data: cardData } = toRefs(props);
const time = computed(() => {
  const time = cardData.value.pubDate || cardData.value.createTime;
  console.log("🚀 ~ time ~ time:", time);
  return time ? time.split(" ")[0] : "-";
});
//根据类别判断是否为视频，目前后端没有区别是否是视频还是电子书
// 目前先用id判断，这有个隐患，后面影音的ID改了，这里需要同步调整
const isVideosCard = computed(() => {
  return props?.materialCategory == 82;
});
// const disableData = ref(cardData.subQueryFlag);
console.log(cardData.value, "-cardData");
const handleCollect = () => {
  if (userStore.id) {
    return fetchCollect();
  }
  ElMessageBox.confirm(
    "登录状态已过期，您可以继续留在该页面，或者重新登录",
    "系统提示",
    {
      confirmButtonText: "重新登录",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    // location.href = import.meta.env.VITE_APP_LOGIN_PATH;
    const loginUrl = `${
      import.meta.env.VITE_APP_SSO_LOGIN_URL
    }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
    if (import.meta.env.MODE == "production") {
      window.open(loginUrl, "_self");
    } else {
      router.push("/login");
    }
  });
};

const fetchCollect = async () => {
  const res = await subscription(cardData.value.id);
  if (res.code == 0) {
    proxy.$message.success({
      message: "收藏成功",
      type: "success",
    });
    cardData.value.subscriptionFlag = 1;
  } else {
    proxy.$message.error(res.error);
  }
};

const handlePreview = () => {
  if (cardData && cardData.value?.materialFile?.fileUrl) {
    addIncreaseClick(cardData.value.id).then((res) => {
      openFile(cardData.value.materialFile.fileUrl, cardData.value);
    });
  } else {
    proxy.$message.error("该文件暂无法查看");
  }
};
</script>
<style scoped lang="scss">
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;

  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;
    // &:hover {
    //   transform: scale(1.2);
    // }
  }

  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}

.ebook-card {
  // width: 100px;
  width: 270px;
  padding-right: 15px;
  padding-bottom: 15px;
}

.ebook-card-img-def {
  position: relative;

  .title {
    cursor: pointer;
    position: absolute;
    // right: 34%;
    right: 10%;
    top: 10px;
    bottom: 10px;
    font-size: 24px;
    font-weight: 500;
    color: #333;
    text-align: center;
    font-family: "微软雅黑", "黑体", sans-serif;
    writing-mode: vertical-rl;
    text-orientation: upright;
  }
}

.ebook-card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 7px;
  margin-top: 15px;
  justify-content: center;
}

.ebook-card-title {
  // display: flex;
  padding-right: 5px;
  width: 150px;
  font-size: 16px;
}

.ebook-card-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 3px;
}

.ebook-card-time {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.ebook-card-collect {
  // width: 40px;
  // height: 20px;
  // padding: 0;
  // font-size: 12px;
}
</style>
