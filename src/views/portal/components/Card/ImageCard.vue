<template>
  <div :style="style" class="image-card-box">
    <div>
      <el-image
        style="width: 360px; height: 260px"
        :title="data?.icon?.fileName"
        :src="data?.icon?.fileUrl"
        fit="fill"
        @click="handlePreview(data)"
      />
    </div>
    <div class="image-card-desc">
      <div class="image-card-text">
        <div class="image-card-title v-line-1">{{ data.materialName }}</div>
        <div
          v-if="!isImage"
          :alt="data.materialDesc"
          class="image-card-content v-line-1"
        >
          资料说明：{{ data.materialDesc ?? "暂无说明" }}
        </div>
        <div class="image-card-date">{{ data.pubDate }}</div>
      </div>
      <div class="image-card-btn">
        <el-button
          :type="isCollected ? 'success' : 'primary'"
          @click="handleCollectClick"
          >{{ isCollected ? "取消收藏" : "收藏" }}</el-button
        >
      </div>
    </div>
  </div>
</template>
<script setup name="ImageCard">
import { ElMessageBox } from "element-plus";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { openFile } from "@/utils/filePreview";
import { cancelSubscription } from "@/api/user.js";
import { subscription } from "@/api/electronicHome.js";
import useUserStore from "@/store/modules/user";
import { watchEffect } from "vue";
import { addIncreaseClick } from "@/api/material";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const router = useRouter();
const props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  style: {
    type: String,
    default: "",
  },
  isImage: {
    type: Boolean,
    default: true,
  },
  btnClick: {
    type: Function,
    default: () => {},
  },
});
const isCollected = ref(props.data.subscriptionFlag == 1 ? true : false);
// 监听 props 的变化来更新 ref
watchEffect(() => {
  isCollected.value = props.data.subscriptionFlag == 1 ? true : false;
});
// 点击收藏处理函数
const handleCollectClick = () => {
  if (userStore.id) {
    proxy.$modal.loading();
    const api = isCollected.value ? cancelSubscription : subscription;
    api(props.data.id)
      .then(() => {
        ElMessage({
          message: `${props.isImage ? "图文" : "影音"}${
            isCollected.value ? "取消收藏" : "收藏"
          }成功`,
          type: "success",
          duration: 3 * 1000,
        });
        props.btnClick && props.btnClick(props.data);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  } else {
    ElMessageBox.confirm("请登录，收藏内容可在我的收藏中查看", "系统提示", {
      confirmButtonText: "重新登录",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      // location.href = import.meta.env.VITE_APP_LOGIN_PATH;
      const loginUrl = `${
        import.meta.env.VITE_APP_SSO_LOGIN_URL
      }?from=${encodeURIComponent(import.meta.env.VITE_APP_BASE_FULL_URL)}`;
      if (import.meta.env.MODE == "production") {
        window.open(loginUrl, "_self");
      } else {
        router.push("/login");
      }
    });
  }
};

const handlePreview = () => {
  addIncreaseClick(props.data.id).then((res) => {
    if (props.isImage) {
      // 构建新页面的URL
      const routeData = router.resolve({
        path: "/portal/imageDesc",
        query: { id: props.data.id, data: JSON.stringify(props.data) },
      });
      // 在新页面中打开
      return window.open(routeData.href, "_blank");
    }
    if (props.data && props.data?.materialFileList?.length > 0) {
      openFile(props.data?.materialFileList[0]?.fileUrl, props.data.value);
    } else {
      proxy.$message.error("该文件暂无法查看");
    }
  });
};
</script>
<style lang="scss" scoped>
.image-card-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 400px;
  padding: 20px;
  .image-card-desc {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: inherit;
    padding: 0 20px;
    margin-top: 10px;
    .image-card-text {
      width: 280px;
      .image-card-title {
        width: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 5px;
        font-size: 16px;
        font-weight: 600;
      }
      .image-card-date {
        font-size: 16px;
      }

      .image-card-content {
        color: #999999;
        font-size: 14px;
      }
    }
    .image-card-btn {
      width: 120px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
</style>
