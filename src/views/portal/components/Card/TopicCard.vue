<template>
  <column-block :title="topicName" :hasMore="true" :moreClick="handClick">
    <div class="topic-box">
      <el-carousel trigger="click" height="365px">
        <el-carousel-item v-for="item in data.list" :key="item.id">
          <div>
            <el-image style="width: 100%; height: 260px" :title="item.title" :src="item.url" fit="fill"
              @click="handleImageClick(item)" />
          </div>
          <div class="title" :title="item.title">
            {{ item.title }}</div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </column-block>
</template>

<script setup name="TopicCardTwo">
import ColumnBlock from "@/views/portal/components/ColumnBlock/ColumnBlock";
import { topicCarousel } from "@/api/topic";
const router = useRouter();
const data = reactive({
  list: []
});
const props = defineProps({
  cardType: {
    type: String,
    default: "paper"
  },
});
const topicName = computed(() => {
  // return "zhuanti";
  return props.type == "paper" ? "线下专题" : "线上专题";
});
const handClick = () => {
  router.push('/portal/topic/' + props.cardType)
};
const fetchCardList = async () => {
  const response = await topicCarousel({
    subjectType:
      props.cardType == "paper" ? "physical_subject" : "electronic_subject",
  });
  if (response.code == 0 && response.data) {
    response.data.forEach((item) => {
      data.list.push({
        id: item.id,
        url: item?.comFile?.fileUrl,
        // url: `${window.location.protocol}//${window.location.host}${item?.comFile?.fileUrl}`,
        fit: item?.fit || "fill",
        title: item.title,
        link: item.webUrl,
      });
    });
  }
};
const handleImageClick = (item) => {
  if(/^http/.test(item?.link)){
     return window.open(item?.link);
  }
  item?.link && router.push(item?.link);
};
onMounted(() => {
  fetchCardList();
});
</script>
<style scoped lang="scss">
.topic-box {
  height: 400px;

  .title {
    background: rgb(242, 242, 242);
    color: #000;
    text-align: left;
    margin-top: -3px;
    padding: 10px 20px;
    line-height: 22px;
    height: 106px;
    width: 100%;
    white-space: pre-line;
    /* 保持换行符的原始形式 */
    text-overflow: ellipsis;
    /* 当内容超出时，显示省略号 */
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
    // display: -webkit-box;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    /* 设置为想要的行数 */
    /* 设置为想要的行数 */
    // overflow: hidden;
    // text-overflow: ellipsis;
    // text-overflow: ellipsis;
  }
}
</style>