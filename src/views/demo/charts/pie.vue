<template>
  <div class="app-container">
    <v-pie 
      idBox="demoPie" 
      :data="chartData" 
      yUnit="个" 
      width="500px" 
      height="350px" 
      :radius="['50%', '70%']" 
    />
  </div>
</template>

<script setup name='demoPie'>
import { ref, onMounted } from "vue";
import VPie from "@/components/Charts/v-pie";
const chartData = ref({});

onMounted(() => {
  chartData.value = {
    columns: ["所属区划", "项目个数"], // , "总投资"
    rows: [
      {
          "所属区划": "跨区项目",
          "项目个数": 23,
          "总投资": "972.15"
      },
      {
          "所属区划": "郑州市",
          "项目个数": 50,
          "总投资": "1783.47"
      },
      {
          "所属区划": "安阳市",
          "项目个数": 30,
          "总投资": "716.02"
      },
      {
          "所属区划": "鹤壁市",
          "项目个数": 15,
          "总投资": "669.40"
      },
      {
          "所属区划": "信阳市",
          "项目个数": 23,
          "总投资": "1060.83"
      },
      {
          "所属区划": "周口市",
          "项目个数": 30,
          "总投资": "807.20"
      },
    ]
  };
})
</script>
<style lang='scss' scoped>

</style>