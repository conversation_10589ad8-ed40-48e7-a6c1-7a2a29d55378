<template>
  <div class="app-container">
    <v-line 
        idBox="demoLine" 
        :data="chartData" 
        width="90%" 
        yUnit="个" 
    />
  </div>
</template>

<script setup name='demoLine'>
import { ref, onMounted } from "vue";
import VLine from "@/components/Charts/v-line";

const chartData = ref({});

onMounted(() => {
  chartData.value = {
    columns: ["所属区划", "名称一", "名称二"],
    rows: [
      {
          "所属区划": "跨区项目",
          "名称一": 23,
          "名称二": 56
      },
      {
          "所属区划": "郑州市",
          "名称一": 50,
          "名称二": 78
      },
      {
          "所属区划": "开封市",
          "名称一": 56,
          "名称二": 99
      },
      {
          "所属区划": "洛阳市",
          "名称一": 53,
          "名称二": 33
      },
      {
          "所属区划": "鹤壁市",
          "名称一": 95,
          "名称二": 34
      },
    ]
  };
})
</script>
