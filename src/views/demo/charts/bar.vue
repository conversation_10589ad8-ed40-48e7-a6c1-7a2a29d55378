<template>
  <div class="app-container">
    <v-bar 
        idBox="demoBar" 
        :data="chartData" 
        width="90%" 
        yUnit="万元" 
    />
  </div>
</template>

<script setup name='demoBar'>
import { ref, onMounted } from "vue";
import VBar from "@/components/Charts/v-bar";
const chartData = ref({});

onMounted(() => {
  chartData.value = {
    columns: ["所属区划", "名称一"],
    rows: [
      {
          "所属区划": "跨区项目",
          "名称一": 23,
          "名称二": "972.15"
      },
      {
          "所属区划": "郑州市",
          "名称一": 50,
          "名称二": "1783.47"
      },
      {
          "所属区划": "开封市",
          "名称一": 27,
          "名称二": "909.26"
      },
      {
          "所属区划": "洛阳市",
          "名称一": 53,
          "名称二": "1662.29"
      },
      {
          "所属区划": "平顶山市",
          "名称一": 32,
          "名称二": "1266.04"
      },
      {
          "所属区划": "三门峡市",
          "名称一": 20,
          "名称二": "551.42"
      },
      {
          "所属区划": "南阳市",
          "名称一": 47,
          "名称二": "1787.17"
      },
      {
          "所属区划": "商丘市",
          "名称一": 26,
          "名称二": "1076.57"
      },
      {
          "所属区划": "信阳市",
          "名称一": 23,
          "名称二": "1060.83"
      },
      {
          "所属区划": "周口市",
          "名称一": 30,
          "名称二": "807.20"
      },
    ]
  };
})
</script>
<style lang='scss' scoped>

</style>