<template>
  <div class="app-container">
    <v-bar-hz
        idBox="demoBar"
        :data="chartData"
        width="90%"
        yUnit="万元"
    />
  </div>
</template>

<script setup name='demoBar'>
import { ref, onMounted } from "vue";
import VBarHz from "@/components/Charts/v-bar-hz";
const chartData = ref({});

onMounted(() => {
  chartData.value = {
    columns: ["所属区划", "名称一"],
    rows: [
      {
          "所属区划": "跨区项目",
          "名称一": 23,
          "名称二": "972.15"
      },
      {
          "所属区划": "郑州市",
          "名称一": 50,
          "名称二": "1783.47"
      },
      {
          "所属区划": "开封市",
          "名称一": 27,
          "名称二": "909.26"
      },
      {
          "所属区划": "洛阳市",
          "名称一": 53,
          "名称二": "1662.29"
      },
    ]
  };
})
</script>
<style lang='scss' scoped>

</style>
