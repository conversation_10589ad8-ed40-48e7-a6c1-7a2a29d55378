<template>
  <div class="home-container">
    <div class="catemana-warp">
      <div class="catemana-table">
        <el-form :model="queryParams" :inline="true" label-width="100">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="资料名称:">
                <el-input
                  class="handle-item"
                  placeholder="请输入资料名称"
                  v-model="queryParams.materialName"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资料时间" prop="year">
                <el-date-picker
                  v-model="queryParams.year"
                  type="year"
                  placeholder="请选择资料时间"
                  value-format="YYYY"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="handleQuery">筛选</el-button>
                <el-button type="primary" @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item>
                <el-button
                  type="primary"
                  class="handle-item"
                  @click="handleOpen(1, null)"
                  >录入资料</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div>
          <el-table border :data="bookList" v-loading="loading">
            <el-table-column label="序号" align="center">
              <template #default="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="图文标题"
              align="center"
              prop="materialName"
            />
            <el-table-column
              label="馆藏"
              align="center"
              prop="materialCategory"
            >
              <template #default="scope"> 图文资料 </template>
            </el-table-column>
            <el-table-column label="资料时间" align="center" prop="pubDate" />
            <el-table-column label="操作" align="center" width="156">
              <template #default="scope">
                <el-button type="primary" @click="handleOpen(2, scope.row)" link
                  >查看</el-button
                >
                <el-button type="primary" @click="handleOpen(0, scope.row)" link
                  >编辑</el-button
                >
                <el-button type="danger" link @click="deleteBook(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <el-dialog v-if="open" v-model="open" width="700px">
      <template #header>{{ title }}</template>
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="catemana-dialog"
        :disabled="status === 2"
      >
        <div class="catemana-dialog-left">
          <el-form-item label="图文标题" prop="materialName">
            <el-input
              v-model="form.materialName"
              placeholder="请输入图文标题"
            />
          </el-form-item>
          <el-form-item label="资料时间" prop="pubDate">
            <el-date-picker
              v-model="form.pubDate"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="图文说明" prop="materialDesc">
            <editor
              v-model="form.materialDesc"
              :min-height="192"
              :disabled="status === 2"
            />
          </el-form-item>
          <el-form-item label="添加图片" prop="materialFileList">
            <imageUpload
              v-model="form.materialFileList"
              :limit="10"
              :fileSize="20"
              :disabled="status === 2"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addBook" v-if="status !== 2"
            >确 定</el-button
          >
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="imagetextManage">
import {
  // getCategoryTree,
  getImageTextList,
  addImageText,
  editImageText,
  deleteImageText,
} from "@/api/admin/imagetext.js";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";

const { proxy } = getCurrentInstance();

const status = ref(null);
const bookList = ref([]);

const total = ref(0);
const open = ref(false);
const title = ref("");
const data = reactive({
  form: {
    pubDate: "",
    materialType: 3,
  },
  loading: false,
  queryParams: {
    current: 1,
    size: 10,
    materialCategory: undefined,
    materialName: undefined,
  },
  rules: {
    materialName: [
      { required: true, message: "图文标题不能为空", trigger: "blur" },
    ],
  },
});
const { queryParams, form, rules, loading } = toRefs(data);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}
function handleReset() {
  queryParams.value = {
    current: 1,
    size: 10,
    materialName: undefined,
  };
  handleQuery();
}
/** 查询用户列表 */
function getList() {
  loading.value = true;
  getImageTextList(queryParams.value)
    .then((res) => {
      bookList.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleOpen(flag, item) {
  if (flag === 0) {
    status.value = flag;
    title.value = "编辑图文";
    form.value = {
      ...item,
    };
  } else if (flag === 1) {
    status.value = flag;
    title.value = "录入图文";
    form.value = {
      materialTexture: 1,
    };
  } else if (flag === 2) {
    status.value = flag;
    title.value = "查看图文";
    form.value = {
      ...item,
    };
  }
  open.value = true;
}
function addBook() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const api = status.value === 1 ? addImageText : editImageText;
      let params = deepClone(form.value);
      // console.log(params)
      // return
      api(params)
        .then(() => {
          getList();

          ElMessage({
            message: "保存成功",
            type: "success",
            duration: 3 * 1000,
          });
          open.value = false;
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
function deleteBook(row) {
  proxy.$modal
    .confirm('是否确认删除为"' + row.materialName + '"的数据项?')
    .then(() => {
      proxy.$modal.loading();
      return deleteImageText(row.id);
    })
    .then(() => {
      getList();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
getList();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .catemana-warp {
    display: flex;
    flex-direction: row;
    .catemana-table {
      flex: 1 1 auto;
      min-height: calc(100vh - 230px);
      :deep(.el-form-item) {
        width: 100%;
      }
      .catemana-handle {
        display: flex;
        padding: 10px 0 20px 0;
        .handle-item {
          margin-right: 20px;
        }
      }
    }
  }
  .catemana-dialog {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 20px;
    width: 100%;
    .catemana-dialog-left {
      // width: 400px;
    }
    .catemana-dialog-right {
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }
      .avatar-uploader .el-upload:hover {
        border-color: #409eff;
      }
      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
      }
      .avatar {
        width: 178px;
        height: 178px;
        display: block;
      }
    }
  }
}
</style>
