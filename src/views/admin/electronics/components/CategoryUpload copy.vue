<template>
  <div class="category-warp">
    <div class="category-tips">
      <div style="display: flex; align-items: center">
        <el-tooltip placement="bottom-start">
          <template #content>类目可拖动排序 </template>
          <el-icon style="margin-right: 10px; vertical-align: top" :size="18">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
        设置说明：当前类目下有资料时，无法删除类目
      </div>
      <div class="category-addbtn">
        <el-button
          type="primary"
          icon="Plus"
          circle
          @click="handleAddCategory(null, true)"
        ></el-button>
      </div>
    </div>
    <TransitionGroup name="list" tag="div" class="drag-container">
      <div
        v-for="(item, index) in categoryAtomList"
        draggable="true"
        @dragstart="dragstart($event, index)"
        @dragenter="dragenter($event, index)"
        @dragend="dragend($event, index)"
        @dragover="dragover($event, index)"
        :key="item.id"
        class="category-item"
      >
        <div class="category-left">
          <div class="category-details">
            <el-form-item label="类型命名:" prop="label" label-width="110">
              <el-input
                v-model="item.label"
                placeholder="请输入类型命名"
                clearable
                disabled
              />
            </el-form-item>
            <el-button
              type="primary"
              icon="EditPen"
              style="margin-left: 20px; margin-right: 10px"
              @click="handleUpdateCategory(item, false)"
            >
              编辑类目
            </el-button>
          </div>
          <div class="category-details">
            <el-form-item label="电子资料总数:" prop="label" label-width="110">
              <el-input v-model="item.showCount" clearable disabled />
            </el-form-item>
          </div>

          <div class="category-list v-m-l-60">
            <div
              v-for="(atomItem, aitomIndex) in item.children"
              class="category-list-item"
            >
              <el-form-item
                :label="`类目${aitomIndex + 1}:`"
                prop="name"
                class="category-list-input"
              >
                <el-input
                  v-model="atomItem.label"
                  placeholder="请输入类目名称"
                  clearable
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
              <el-button
                type="primary"
                icon="Delete"
                circle
                @click="handleDelete(atomItem)"
              ></el-button>
            </div>
          </div>
        </div>
        <div class="category-right">
          <el-button
            type="primary"
            icon="Delete"
            circle
            @click="handleDelete(item)"
          ></el-button>
        </div>
      </div>
    </TransitionGroup>
    <el-dialog v-model="openAdd">
      <template #header>{{ title }}</template>
      <el-form ref="addForm" :model="formAdd" :rules="rulesAdd">
        <el-form-item label="类目名称" prop="categoryName" label-width="110">
          <el-input
            v-model="formAdd.categoryName"
            placeholder="请输入类目名称"
          />
        </el-form-item>
        <el-form-item
          v-if="formAdd.parentId == 0"
          label="电子资料总数:"
          prop="showCount"
          label-width="110"
        >
          <el-input v-model="formAdd.showCount" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addCategory">确 定</el-button>
          <el-button @click="openAdd = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="openUpdate">
      <template #header>
        <div style="display: flex; align-items: center; cursor: pointer">
          {{ title }}
          <el-tooltip placement="bottom-start">
            <template #content>子类目可拖动排序 </template>
            <el-icon style="margin-left: 10px; vertical-align: top" :size="18">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
      <el-form ref="updateForm" :model="formUpdate" :rules="rulesAdd">
        <el-form-item>
          <el-button
            type="primary"
            icon="Plus"
            style="margin-left: 20px"
            @click="handleAddCategory(formUpdate, true)"
          >
            添加类目
          </el-button>
        </el-form-item>
        <el-form-item label="类目名称" prop="categoryName" label-width="110">
          <el-input
            v-model="formUpdate.categoryName"
            placeholder="请输入类目名称"
          />
        </el-form-item>
        <el-form-item
          v-if="
            (formUpdate.type && formUpdate.parentId === 0) || !formUpdate.type
          "
          label="电子资料总数:"
          prop="showCount"
          label-width="110"
        >
          <el-input v-model="formUpdate.showCount" clearable />
        </el-form-item>
        <el-divider
          v-if="formUpdate.children && formUpdate.children.length > 0"
          content-position="left"
          >子类目</el-divider
        >
        <TransitionGroup
          name="list"
          tag="div"
          class="drag-container"
          style="width: 100%"
        >
          <div
            v-for="(atomItem, aitomIndex) in formUpdate.children"
            class="category-list-item"
            draggable="true"
            @dragstart="dragstart($event, aitomIndex)"
            @dragenter="dragenterSub($event, aitomIndex)"
            @dragend="dragendSub($event, aitomIndex)"
            @dragover="dragover($event, aitomIndex)"
            :key="atomItem.id"
          >
            <el-form-item
              :label="`类目${aitomIndex + 1}:`"
              prop="name"
              class="category-list-input"
              label-width="110"
              style="cursor: row-resize; padding-right: 20px"
            >
              <el-input
                v-model="atomItem.categoryName"
                placeholder="请输入类目名称"
                clearable
                style="width: 100%"
              />
            </el-form-item>
            <el-button
              type="primary"
              icon="Delete"
              circle
              @click="handleDelete(atomItem)"
            ></el-button>
          </div>
        </TransitionGroup>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="updateCategory">确 定</el-button>
          <el-button @click="openUpdate = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="CategoryUpload">
import {
  getCategoryTree,
  getCategoryNode,
  addCategoryNode,
  deleteCategoryNode,
  updateCategoryNode,
} from "@/api/admin/electronics.js";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();
const dragIndex = ref(0);
const openAdd = ref(false);
const title = ref("添加类目");
const categoryAtomList = ref([]);
const formAdd = ref({});
const rulesAdd = ref({
  categoryName: [
    { required: true, message: "类目名称不能为空", trigger: "blur" },
  ],
});
const openUpdate = ref(false);
const formUpdate = ref({});

const props = defineProps({
  categoryType: {
    type: Number,
    default: 1,
  },
});

/**
 * 拖拽排序
 */
function dragstart(e, index) {
  e.stopPropagation();
  dragIndex.value = index;
  setTimeout(() => {
    e.target.classList.add("moveing");
  }, 0);
}

function dragenter(e, index) {
  e.preventDefault();
  // 拖拽到原位置时不触发
  if (dragIndex.value !== index) {
    const source = categoryAtomList.value[dragIndex.value];
    categoryAtomList.value.splice(dragIndex.value, 1);
    categoryAtomList.value.splice(index, 0, source);
    // 更新节点位置
    dragIndex.value = index;
  }
}
function dragenterSub(e, index) {
  e.preventDefault();
  // 拖拽到原位置时不触发
  if (dragIndex.value !== index) {
    const source = formUpdate.value.children[dragIndex.value];
    formUpdate.value.children.splice(dragIndex.value, 1);
    formUpdate.value.children.splice(index, 0, source);
    // 更新节点位置
    dragIndex.value = index;
  }
}
function dragover(e, index) {
  console.log("over", index);
  e.preventDefault();
  e.dataTransfer.dropEffect = "move";
}
function dragend(e, index) {
  e.target.classList.remove("moveing");
  sortCategory();
}
function dragendSub(e, index) {
  e.target.classList.remove("moveing");
  sortSubCategory();
}

function handleGetCategory() {
  proxy.$modal.loading();
  getCategoryTree({
    categoryType: props.categoryType,
  })
    .then((res) => {
      categoryAtomList.value = res.data;
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

// 类目新增
function handleAddCategory(item) {
  formAdd.value = {
    //categoryType 1 电子， 2实体
    categoryType: props.categoryType,
    parentId: item?.id || 0,
  };
  title.value = "添加类目";
  openAdd.value = true;
}

// 类目修改
async function handleUpdateCategory(item) {
  title.value = "修改类目";
  const { data } = await getCategoryNode(item.id);
  formUpdate.value = {
    ...data,
  };
  openUpdate.value = true;
}
// flag = true时为新增， false为修改
function addCategory() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      addCategoryNode(formAdd.value)
        .then(() => {
          if (formAdd.value.parentId) {
            //update
            handleUpdateCategory(formUpdate.value);
          }
          openAdd.value = false;
          ElMessage({
            message: `新增成功`,
            type: "success",
            duration: 3 * 1000,
          });
          handleGetCategory();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
function updateCategory() {
  proxy.$refs["updateForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      updateCategoryNode(formUpdate.value)
        .then(() => {
          openUpdate.value = false;
          ElMessage({
            message: `编辑成功`,
            type: "success",
            duration: 3 * 1000,
          });
          handleGetCategory();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}

const handleDelete = (item) => {
  proxy.$modal
    .confirm("是否确认删除该类目?")
    .then(() => {
      proxy.$modal.loading();
      return deleteCategoryNode(item.id);
    })
    .then(() => {
      if (item.parentId != 0 && openUpdate.value == true) {
        handleUpdateCategory(formUpdate.value);
      }
      handleGetCategory();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
};
const sortSubCategory = () => {
  formUpdate.value.children.forEach((element, index) => {
    console.log(element.orderNum, index + 10);
    if (element.orderNum != index + 10) {
      element.orderNum = index + 10;
    }
  });
};
const sortCategory = () => {
  proxy.$modal.loading();
  const preUpdateList = [];
  categoryAtomList.value.forEach((element, index) => {
    console.log(element.orderNum, index + 10);
    if (element.orderNum != index + 10) {
      preUpdateList.push(
        sortDate({
          id: element.id,
          parentId: element.parentId,
          categoryName: element?.label,
          showCount: element?.showCount,
          children: element?.children,
          orderNum: index + 10,
          categoryType: props.categoryType,
        })
      );
    }
  });
  Promise.all([])
    .then(() => {
      ElMessage({
        message: `调整成功`,
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
      handleGetCategory();
    });
};
const sortDate = (data) => {
  return updateCategoryNode(data);
};
handleGetCategory();
</script>
<style lang="scss" scoped>
.category-warp {
  .category-tips {
    display: flex;
    flex-direction: row;
    align-items: center;
    .category-addbtn {
      margin-left: 20px;
    }
  }
  .category-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px 10px 0;
    cursor: row-resize;
    .category-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      flex: 1 0 0;
      border: 2px solid rgb(205, 205, 205);
      padding: 25px;
      border-radius: 10px;
      &:hover {
        background-color: rgb(242, 242, 242);
      }
      .category-details {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        :deep(.el-form-item) {
          margin-bottom: 0px;
        }
      }
      .category-list {
        // display: flex;
        // flex-direction: row;
        // align-items: center;
        // flex-wrap: wrap;
        // width: 100%;
        // justify-content: flex-start;
        // .category-list-item {
        //   display: flex;
        //   // flex-direction: row;
        //   // width: 45%;
        //   margin-right: 5%;
        //   .category-list-input {
        //     // flex: 1 1 auto;
        //     margin-right: 10px;
        //     // width: 320px;
        //   }
        // }
      }
      .carousel-upload {
        width: 240px;
        margin-right: 20px;
      }
    }
    .category-right {
      width: 70px;
      display: flex;
      flex-direction: row;
      justify-content: center;
    }
  }
}

.category-list-item {
  display: flex;
  // flex-direction: row;
  // width: 45%;
  margin-right: 5%;
  width: 90%;
  cursor: row-resize;
  :deep(.el-form .el-form-item__label) {
    cursor: row-resize;
  }
  .category-list-input {
    flex: 1 0 auto;
    margin-right: 10px;
    // cursor: row-resize;
    // width: 320px;
  }
}

.list-move, /* 对移动中的元素应用的过渡 */
    .list-enter-active,
    .list-leave-active {
  transition: all 0.2s ease;
}

.moveing {
  opacity: 0;
}

.drag-container {
  position: relative;
  padding: 0;
}
</style>
