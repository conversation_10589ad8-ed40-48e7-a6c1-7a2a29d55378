<template>
  <div class="category-warp">
    <div class="category-tips">
      <div style="display: flex; align-items: center">
        <el-tooltip placement="bottom-start">
          <template #content>类目可拖动排序 </template>
          <el-icon style="margin-right: 10px; vertical-align: top" :size="18">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
        设置说明：当前类目下有资料时，无法删除类目
      </div>
      <div class="category-addbtn">
        <el-button
          type="primary"
          icon="Plus"
          circle
          @click="handleAddCategory(null, true)"
        ></el-button>
      </div>
    </div>
    <TransitionGroup name="list" tag="div" class="drag-container">
      <div
        v-for="(item, index) in categoryAtomList"
        draggable="false"
        @dragstart="dragstart($event, index)"
        @dragenter="dragenter($event, index)"
        @dragend="dragend($event, index)"
        @dragover="dragover($event, index)"
        :key="item.id"
        class="category-item"
      >
        <div class="category-left">
          <div class="category-details">
            <el-form-item label="类型命名:" prop="label" label-width="110">
              <el-input
                v-model="item.label"
                placeholder="请输入类型命名"
                clearable
                disabled
              />
            </el-form-item>
            <el-button
              type="primary"
              icon="EditPen"
              style="margin-left: 20px; margin-right: 10px"
              @click="handleUpdateCategory(item, false)"
            >
              编辑类目
            </el-button>

            <!-- <el-form-item
              class="v-m-l-20"
              v-if="categoryType == 1"
              label="是否需要登录:"
              prop="label"
              label-width="110"
            >
              <el-switch v-model="item.needLogin" />
            </el-form-item> -->
            <el-button
              type="text"
              style="margin-left: 20px; margin-right: 10px"
              @click="item.expand = !item.expand"
            >
              {{ item.expand ? "折叠" : "展开" }}
            </el-button>
          </div>
          <div v-show="item.expand">
            <div class="category-details" v-if="categoryType == 1">
              <el-form-item
                label="电子资料总数:"
                prop="label"
                label-width="110"
              >
                <el-input v-model="item.showCount" clearable disabled />
              </el-form-item>
            </div>

            <div class="category-list v-m-l-60">
              <div
                v-for="(atomItem, aitomIndex) in item.children"
                class="category-list-item"
              >
                <div class="list-item-line">
                  <el-form-item
                    :label="`类目${aitomIndex + 1}:`"
                    prop="name"
                    class="category-list-input"
                  >
                    <el-input
                      v-model="atomItem.label"
                      placeholder="请输入类目名称"
                      clearable
                      disabled
                      style="width: 100%"
                    />
                  </el-form-item>
                  <el-button
                    type="danger"
                    icon="Delete"
                    circle
                    @click="handleDelete(atomItem)"
                  ></el-button>
                </div>
                <div
                  v-for="(threeItem, threeIndex) in atomItem.children"
                  class="category-list-item"
                  :key="threeItem.id"
                >
                  <div class="list-item-line">
                    <el-form-item
                      :label="`三级类目${threeIndex + 1}:`"
                      prop="name"
                      class="category-list-input"
                      label-width="110"
                      style="cursor: row-resize; padding-right: 0px"
                    >
                      <el-input
                        v-model="threeItem.label"
                        placeholder="请输入类目名称"
                        clearable
                        disabled
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-button
                      type="danger"
                      icon="Delete"
                      circle
                      @click="handleDelete(threeItem)"
                    ></el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="category-right">
          <el-button type="text" circle @click="handleDelete(item)"
            >删除</el-button
          >
          <el-button
            type="text"
            circle
            v-if="index != 0"
            @click="handleSort(index, 'up', categoryAtomList)"
            >上移</el-button
          >
          <el-button
            type="text"
            circle
            v-if="index != categoryAtomList.length - 1"
            @click="handleSort(index, 'down', categoryAtomList)"
            >下移</el-button
          >
        </div>
      </div>
    </TransitionGroup>
    <el-dialog v-model="openAdd">
      <template #header>{{ title }}</template>
      <el-form ref="addForm" :model="formAdd" :rules="rulesAdd">
        <el-form-item label="类目名称" prop="categoryName" label-width="110">
          <el-input
            v-model="formAdd.categoryName"
            placeholder="请输入类目名称"
          />
        </el-form-item>
        <el-form-item
          v-if="formAdd.parentId == 0"
          label="电子资料总数:"
          prop="showCount"
          label-width="110"
        >
          <el-input v-model="formAdd.showCount" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addCategory">确 定</el-button>
          <el-button @click="openAdd = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="openUpdate">
      <template #header>
        <div style="display: flex; align-items: center; cursor: pointer">
          {{ title }}
          <el-tooltip placement="bottom-start">
            <template #content>子类目可拖动排序 </template>
            <el-icon style="margin-left: 10px; vertical-align: top" :size="18">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
      <el-form ref="updateForm" :model="formUpdate" :rules="rulesAdd">
        <el-form-item>
          <el-button
            type="primary"
            icon="Plus"
            style="margin-left: 20px"
            @click="handleAddCategory(formUpdate, true)"
          >
            添加类目
          </el-button>
        </el-form-item>
        <el-form-item label="类目名称" prop="categoryName" label-width="110">
          <el-input
            v-model="formUpdate.categoryName"
            placeholder="请输入类目名称"
          />
        </el-form-item>
        <el-form-item
          v-if="categoryType == 1"
          label="是否需要登录:"
          prop="label"
          label-width="110"
        >
          <el-switch
            :active-value="1"
            :inactive-value="0"
            v-model="formUpdate.loginFlag"
          />
        </el-form-item>

        <el-form-item
          v-if="
            (formUpdate.type && formUpdate.parentId === 0) || !formUpdate.type
          "
          label="电子资料总数:"
          prop="showCount"
          label-width="110"
        >
          <el-input v-model="formUpdate.showCount" clearable />
        </el-form-item>
        <el-divider
          v-if="formUpdate.children && formUpdate.children.length > 0"
          content-position="left"
          >子类目</el-divider
        >
        <TransitionGroup
          name="list"
          tag="div"
          class="drag-container"
          style="width: 100%"
        >
          <div
            v-for="(atomItem, aitomIndex) in formUpdate.children"
            class="category-list-item"
            draggable="false"
            @dragstart="dragstart($event, aitomIndex)"
            @dragenter="dragenterSub($event, aitomIndex)"
            @dragend="dragendSub($event, aitomIndex)"
            @dragover="dragover($event, aitomIndex)"
            :key="atomItem.id"
          >
            <div class="list-item-line">
              <el-form-item
                :label="`类目${aitomIndex + 1}:`"
                prop="name"
                class="category-list-input"
                label-width="110"
                style="cursor: row-resize; padding-right: 20px"
              >
                <el-input
                  v-model="atomItem.categoryName"
                  placeholder="请输入类目名称"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
              <div style="width: 164px">
                <el-button type="text" circle @click="handleDelete(atomItem)"
                  >删除</el-button
                >
                <el-button type="text" circle @click="handleThreeAdd(atomItem)"
                  >新增</el-button
                >
                <el-button
                  v-if="aitomIndex != 0"
                  type="text"
                  circle
                  @click="handleSort(aitomIndex, 'up', formUpdate.children)"
                  >上移</el-button
                >
                <el-button
                  type="text"
                  circle
                  v-if="aitomIndex != formUpdate.children.length - 1"
                  @click="handleSort(aitomIndex, 'down', formUpdate.children)"
                  >下移</el-button
                >
              </div>
            </div>
            <TransitionGroup
              name="list"
              tag="div"
              class="list-item-child"
              style="width: 100%"
            >
              <div
                class="category-list-item"
                draggable="false"
                v-for="(threeItem, threeIndex) in atomItem.children"
                @dragstart="dragstart($event, threeIndex)"
                @dragenter="dragenterSub($event, threeIndex)"
                @dragend="dragendSub($event, threeIndex)"
                @dragover="dragover($event, threeIndex)"
                :key="threeItem.id"
              >
                <div class="list-item-line">
                  <el-form-item
                    :label="`三级类目${threeIndex + 1}:`"
                    prop="name"
                    class="category-list-input"
                    label-width="110"
                    style="cursor: row-resize; padding-right: 20px"
                  >
                    <el-input
                      v-model="threeItem.categoryName"
                      placeholder="请输入类目名称"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                  <div style="width: 120px">
                    <el-button
                      type="text"
                      circle
                      @click="handleDelete(threeItem)"
                      >删除</el-button
                    >

                    <el-button
                      v-if="threeIndex != 0"
                      type="text"
                      circle
                      @click="handleSort(threeIndex, 'up', atomItem.children)"
                      >上移</el-button
                    >
                    <el-button
                      type="text"
                      circle
                      v-if="threeIndex != atomItem.children.length - 1"
                      @click="handleSort(threeIndex, 'down', atomItem.children)"
                      >下移</el-button
                    >
                  </div>
                </div>
              </div>
            </TransitionGroup>
          </div>
        </TransitionGroup>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="updateCategory">确 定</el-button>
          <el-button @click="openUpdate = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="CategoryUpload">
import {
  getCategoryTree,
  getCategoryNode,
  addCategoryNode,
  deleteCategoryNode,
  updateCategoryNode,
  categorySort,
} from "@/api/admin/electronics.js";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();
const dragIndex = ref(0);
const openAdd = ref(false);
const title = ref("添加类目");
const categoryAtomList = ref([]);
const formAdd = ref({});
const rulesAdd = ref({
  categoryName: [
    { required: true, message: "类目名称不能为空", trigger: "blur" },
  ],
});
const openUpdate = ref(false);
const formUpdate = ref({});

const props = defineProps({
  categoryType: {
    type: Number,
    default: 1,
  },
});

/**
 * 拖拽排序
 */
function dragstart(e, index) {
  e.stopPropagation();
  dragIndex.value = index;
  setTimeout(() => {
    e.target.classList.add("moveing");
  }, 0);
}

function dragenter(e, index) {
  e.preventDefault();
  // 拖拽到原位置时不触发
  if (dragIndex.value !== index) {
    const source = categoryAtomList.value[dragIndex.value];
    categoryAtomList.value.splice(dragIndex.value, 1);
    categoryAtomList.value.splice(index, 0, source);
    // 更新节点位置
    dragIndex.value = index;
  }
}
function dragenterSub(e, index) {
  e.preventDefault();
  // 拖拽到原位置时不触发
  if (dragIndex.value !== index) {
    const source = formUpdate.value.children[dragIndex.value];
    formUpdate.value.children.splice(dragIndex.value, 1);
    formUpdate.value.children.splice(index, 0, source);
    // 更新节点位置
    dragIndex.value = index;
  }
}
function dragover(e, index) {
  console.log("over", index);
  e.preventDefault();
  e.dataTransfer.dropEffect = "move";
}
function dragend(e, index) {
  e.target.classList.remove("moveing");
  sortCategory();
}
function dragendSub(e, index) {
  e.target.classList.remove("moveing");
  sortSubCategory();
}

function handleGetCategory() {
  proxy.$modal.loading();
  getCategoryTree({
    categoryType: props.categoryType,
  })
    .then((res) => {
      categoryAtomList.value = res.data;
      if (openUpdate.value) {
        updateCategoryData();
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

// 类目新增
function handleAddCategory(item) {
  formAdd.value = {
    //categoryType 1 电子， 2实体
    categoryType: props.categoryType,
    parentId: item?.id || 0,
  };
  title.value = "添加类目";
  openAdd.value = true;
}
const currentNode = ref(null);
// 类目修改
async function handleUpdateCategory(item) {
  currentNode.value = item;
  title.value = "修改类目";
  openUpdate.value = true;
  updateCategoryData();
}
function updateCategoryData() {
  /* const { data } = await getCategoryNode(item.id);
  console.log(data) */
  const list = categoryAtomList.value.filter(
    (obj) => currentNode.value.id === obj.id
  );
  console.log(list);
  console.log(replaceLabelWithCategoryName(list));
  formUpdate.value = {
    categoryType: props.categoryType,
    ...replaceLabelWithCategoryName(list)[0],
  };
}
//处理树状结构的label为categoryName
function replaceLabelWithCategoryName(data) {
  return data.map((item) => {
    const newItem = { ...item };
    if (newItem.label !== undefined) {
      newItem.categoryName = newItem.label;
      delete newItem.label;
    }
    if (newItem.children && newItem.children.length > 0) {
      newItem.children = replaceLabelWithCategoryName(newItem.children);
    }
    return newItem;
  });
}
// flag = true时为新增， false为修改
function addCategory() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      addCategoryNode(formAdd.value)
        .then(() => {
          if (formAdd.value.parentId) {
            //update
            handleUpdateCategory(formUpdate.value);
          }
          openAdd.value = false;
          ElMessage({
            message: `新增成功`,
            type: "success",
            duration: 3 * 1000,
          });
          if (title.value == "添加三级类目" && openUpdate.value) {
            return;
          }
          handleGetCategory();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
function updateCategory() {
  proxy.$refs["updateForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      updateCategoryNode(formUpdate.value)
        .then(() => {
          openUpdate.value = false;
          ElMessage({
            message: `编辑成功`,
            type: "success",
            duration: 3 * 1000,
          });
          handleGetCategory();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
const handleDelete = (item) => {
  console.log(item);
  proxy.$modal
    .confirm(
      h("p", null, [
        h("span", null, "是否确认删除"),
        h(
          "span",
          { style: "color: #f00" },
          item.categoryName ? item.categoryName : item.label
        ),
        h("span", null, "该类目?"),
      ])
    )
    .then(() => {
      proxy.$modal.loading();
      return deleteCategoryNode(item.id);
    })
    .then(() => {
      if (item.parentId != 0 && openUpdate.value == true) {
        handleUpdateCategory(formUpdate.value);
      }
      handleGetCategory();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
};
const handleThreeAdd = (item) => {
  console.log(item);
  formAdd.value = {
    categoryName: "",
    //categoryType 1 电子， 2实体
    categoryType: props.categoryType,
    parentId: item?.id || 0,
  };
  title.value = "添加三级类目";
  openAdd.value = true;
};
const sortSubCategory = () => {
  formUpdate.value.children.forEach((element, index) => {
    console.log(element.orderNum, index + 10);
    if (element.orderNum != index + 10) {
      element.orderNum = index + 10;
    }
  });
};
const sortCategory = () => {
  proxy.$modal.loading();
  const preUpdateList = [];
  categoryAtomList.value.forEach((element, index) => {
    console.log(element.orderNum, index + 10);
    if (element.orderNum != index + 10) {
      preUpdateList.push(
        sortDate({
          id: element.id,
          parentId: element.parentId,
          categoryName: element?.label,
          showCount: element?.showCount,
          children: element?.children,
          orderNum: index + 10,
          categoryType: props.categoryType,
        })
      );
    }
  });
  Promise.all([])
    .then(() => {
      ElMessage({
        message: `调整成功`,
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
      handleGetCategory();
    });
};
const sortDate = (data) => {
  return updateCategoryNode(data);
};

const handleSort = (index, direction, arr, type = "1") => {
  let params = [];
  if (!Array.isArray(arr)) return;
  if (direction === "up" && index > 0) {
    // 交换元素
    [arr[index - 1], arr[index]] = [arr[index], arr[index - 1]];
    // 交换orderNum
    const tempOrder = arr[index].orderNum;
    arr[index].orderNum = arr[index - 1].orderNum;
    arr[index - 1].orderNum = tempOrder;
    // 记录互换后的id和orderNum
    params = [
      { id: arr[index].id, orderNum: arr[index].orderNum },
      { id: arr[index - 1].id, orderNum: arr[index - 1].orderNum },
    ];
  } else if (direction === "down" && index < arr.length - 1) {
    // 交换元素
    [arr[index], arr[index + 1]] = [arr[index + 1], arr[index]];
    // 交换orderNum
    const tempOrder = arr[index].orderNum;
    arr[index].orderNum = arr[index + 1].orderNum;
    arr[index + 1].orderNum = tempOrder;
    // 记录互换后的id和orderNum
    params = [
      { id: arr[index].id, orderNum: arr[index].orderNum },
      { id: arr[index + 1].id, orderNum: arr[index + 1].orderNum },
    ];
  }
  categorySort(params).then(() => {
    if (
      ["修改类目", "添加二级类目", "添加三级类目"].includes(title.value) &&
      openUpdate.value
    ) {
      return;
    }
    handleGetCategory();
    ElMessage({
      message: `调整成功`,
      type: "success",
      duration: 3 * 1000,
    });
  });
};
handleGetCategory();
</script>
<style lang="scss" scoped>
.category-warp {
  .category-tips {
    display: flex;
    flex-direction: row;
    align-items: center;
    .category-addbtn {
      margin-left: 20px;
    }
  }
  .category-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px 10px 0;
    cursor: row-resize;
    .category-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      flex: 1 0 0;
      border: 2px solid rgb(205, 205, 205);
      padding: 25px;
      border-radius: 10px;
      &:hover {
        background-color: rgb(242, 242, 242);
      }
      .category-details {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        :deep(.el-form-item) {
          margin-bottom: 0px;
        }
      }
      .category-list {
        // display: flex;
        // flex-direction: row;
        // align-items: center;
        // flex-wrap: wrap;
        // width: 100%;
        // justify-content: flex-start;
        // .category-list-item {
        //   display: flex;
        //   // flex-direction: row;
        //   // width: 45%;
        //   margin-right: 5%;
        //   .category-list-input {
        //     // flex: 1 1 auto;
        //     margin-right: 10px;
        //     // width: 320px;
        //   }
        // }
      }
      .carousel-upload {
        width: 240px;
        margin-right: 20px;
      }
    }
    .category-right {
      width: 150px;
      display: flex;
      flex-direction: row;
      justify-content: left;
      margin-left: 20px;
    }
  }
}

.category-list-item {
  // flex-direction: row;
  // width: 45%;
  margin-right: 5%;
  width: 100%;
  cursor: row-resize;
  :deep(.el-form .el-form-item__label) {
    cursor: row-resize;
  }
  .category-list-input {
    width: 100%;
    flex: 1;
    margin-right: 10px;
    // cursor: row-resize;
    // width: 320px;
  }
  .list-item-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    .el-form-item--default {
      margin-bottom: 0;
    }
  }
  .list-item-child {
    width: calc(100% - 100px) !important;
    margin-left: 56px;
  }
}

.list-move, /* 对移动中的元素应用的过渡 */
    .list-enter-active,
    .list-leave-active {
  transition: all 0.2s ease;
}

.moveing {
  opacity: 0;
}

.drag-container {
  position: relative;
  padding: 0;
}
</style>
