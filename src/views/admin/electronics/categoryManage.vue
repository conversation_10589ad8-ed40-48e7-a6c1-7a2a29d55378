<template>
  <div class="home-container">
    <div class="catemana-warp">
      <div class="catemana-table">
        <el-form :model="queryParams" :inline="true" label-width="115">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="资料名称">
                <el-input
                  class="handle-item"
                  placeholder="请输入资料名称"
                  v-model="queryParams.materialName"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <!-- <el-form-item label="资料类目">
                <el-tree-select
                  v-model="queryParams.materialCategory"
                  :data="categoryOptions"
                  :props="{ value: 'id', label: 'label', children: 'children' }"
                  value-key="id"
                  placeholder="请选择资料所属类目"
                  check-strictly
                  style="width: 100%"
                />
              </el-form-item> -->
              <el-form-item label="资料类目" prop="materialCategory">
                <el-cascader
                  style="width: 100%"
                  v-model="queryParams.materialCategory"
                  :options="categoryOptions"
                  :props="classifyProps"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="馆藏分类">
                <el-select
                  v-model="queryParams.materialCluster"
                  placeholder="请选择馆藏分类"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in electronic_cluster_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- </el-row>
          <el-row :gutter="20"> -->
            <!-- <el-col :span="8">
              <el-form-item label="资料类型" prop="materialType">
                <el-select
                  v-model="queryParams.materialType"
                  placeholder="请选择资料类型"
                >
                  <el-option
                    v-for="dict in electronic_material_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="年份" prop="materialYear">
                <el-date-picker
                  v-model="queryParams.materialYear"
                  type="year"
                  placeholder="请选择资料年份"
                  value-format="YYYY"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="录入时间" prop="createTimeStart">
                <el-date-picker
                  type="daterange"
                  value-format="YYYY-MM-DD"
                  v-model="queryParams.createTimeStart"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="handleQuery">筛选</el-button>
                <el-button type="primary" @click="handleReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="['1'].includes(pageType)">
            <el-col :span="8">
              <el-form-item>
                <el-button
                  type="primary"
                  class="handle-item"
                  @click="handleOpen(1, null)"
                  >录入资料</el-button
                >
                <el-popover
                  placement="top-start"
                  title=""
                  :width="430"
                  trigger="hover"
                  content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
                >
                  <template #reference>
                    <el-button
                      type="info"
                      plain
                      icon="Download"
                      @click="handleExport"
                      >导出资料清单</el-button
                    >
                  </template>
                </el-popover>
                <el-button
                  type="danger"
                  v-if="['1'].includes(pageType)"
                  plain
                  icon="Delete"
                  :disabled="multiple"
                  @click="deleteBook"
                  >删除</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div>
          <el-table
            :data="bookList"
            border
            v-loading="loading"
            @sort-change="sortChange"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              v-if="['1'].includes(pageType)"
              type="selection"
              width="50"
              align="center"
            />
            <el-table-column label="序号" width="80" align="center">
              <template #default="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="资料名称"
              align="center"
              prop="materialName"
              min-width="150"
            />
            <el-table-column label="分类 —— 类目 —— 子类" align="center">
              <template #default="scope">
                {{ replaceStr(scope.row.categoryNames) }}
              </template>
            </el-table-column>
            <el-table-column
              label="录入时间"
              align="center"
              width="100"
              prop="createTime"
              v-if="['1'].includes(pageType)"
            />
            <el-table-column
              label="年份"
              v-if="['1'].includes(pageType)"
              align="center"
              prop="materialYear"
            />
            <el-table-column
              label="累计预览次数"
              align="center"
              prop="clickNum"
              sortable
              v-if="['2'].includes(pageType)"
            />
            <el-table-column
              label="累计收藏次数"
              v-if="['2'].includes(pageType)"
              align="center"
              sortable
              prop="subNum"
            />
            <el-table-column
              label="操作"
              align="center"
              width="180"
              v-if="['1'].includes(pageType)"
            >
              <template #default="scope">
                <el-button type="primary" @click="handleOpen(2, scope.row)" link
                  >查看</el-button
                >
                <el-button type="primary" @click="handleOpen(0, scope.row)" link
                  >编辑</el-button
                >
                <el-button type="danger" link @click="deleteBook(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <el-dialog v-if="open" v-model="open" width="700px">
      <template #header>{{ title }}</template>
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="catemana-dialog"
        :disabled="status === 2"
      >
        <div class="catemana-dialog-left">
          <el-form-item label="资料名称" prop="materialName">
            <el-input
              v-model="form.materialName"
              placeholder="请输入资料名称"
            />
          </el-form-item>
          <el-form-item label="资料所属类目" prop="materialCategory">
            <el-tree-select
              v-model="form.materialCategory"
              :data="categoryOptions"
              :props="{ value: 'id', label: 'label', children: 'children' }"
              value-key="id"
              placeholder="请选择所属类目"
              check-strictly
            />
          </el-form-item>
          <!-- <el-form-item label="资料所属馆藏分类" prop="materialCluster">
            <el-select
              v-model="form.materialCluster"
              placeholder="请选择馆藏分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in electronic_cluster_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="资料质地" prop="materialTexture">
            <el-select v-model="form.materialTexture" disabled>
              <el-option label="电子" :value="1"></el-option>
              <el-option label="实体" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="资料类型" prop="materialType">
            <el-select v-model="form.materialType">
              <el-option
                v-for="dict in electronic_material_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item v-if="status !== 2" label="上传选项">
            <el-radio v-model="type" :label="0">文件上传</el-radio>
            <el-radio v-model="type" :label="1">文件夹上传</el-radio>
          </el-form-item> -->
          <el-form-item
            v-if="form.materialType === 1"
            label="资料路径"
            prop="materialPath"
          >
            <el-input
              v-model="form.materialPath"
              placeholder="请输入资料路径"
            />
          </el-form-item>
          <el-form-item label="资料内容" prop="materialFileList">
            <div v-if="status !== 2">
              <FileUploadChunk
                v-model="form.materialFileList"
                :fileType="['zip', 'doc', 'docx', 'xls', 'xlsx', 'pdf']"
                :fileSize="0"
                v-if="type === 0"
                :limit="1"
                ref="fileUploadRef"
              ></FileUploadChunk>
              <FolderUpload
                v-model="form.materialFileList"
                v-if="type === 1"
              ></FolderUpload>
            </div>
            <div v-else>
              <el-button type="primary" link>{{
                form.materialFile?.fileName
              }}</el-button>
            </div>
          </el-form-item>
          <el-form-item label="资料年份" prop="materialYear">
            <el-date-picker
              v-model="form.materialYear"
              type="year"
              value-format="YYYY"
            ></el-date-picker>
          </el-form-item>
        </div>
        <div class="catemana-dialog-right">
          <el-form-item prop="iconList" style="width: 150px">
            <imageUpload v-model="form.iconList" :limit="1" :fileSize="10" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addBook" v-if="status !== 2"
            >确 定</el-button
          >
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="categoryManage">
import {
  getCategoryTree,
  getElectronicBookList,
  addCategoryBook,
  updateCategoryBook,
  deleteCategoryBook,
  exportMaterialElectronic,
  statisticSelectronic,
} from "@/api/admin/electronics.js";
import FileUploadChunk from "@/components/FileUploadChunk";
import FolderUpload from "@/components/FolderUpload";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";
import { nextTick } from "vue";
import { replaceStr } from "@/utils/validate";
const { proxy } = getCurrentInstance();
/* const { electronic_cluster_type, electronic_material_type } = proxy.useDict(
  "electronic_cluster_type",
  "electronic_material_type"
); */
const route = useRoute();
const pageType = computed(() => {
  // 1-资料管理 2-资料统计
  return route.query.pageType ?? 1;
});

const categoryOptions = ref(undefined);
const status = ref(null);
const bookList = ref([]);
const bookTypeList = computed(() => {
  let temp = [];
  categoryOptions.value.forEach((element) => {
    temp.push({
      id: element.id,
      label: element.label,
    });
    if (element.children) temp.push(...element.children);
  });
  return temp;
});
const classifyProps = {
  expandTrigger: "hover",
  value: "id",
  checkStrictly: true,
};
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const type = ref(0);
const total = ref(0);
const open = ref(false);
const title = ref("");
const data = reactive({
  form: {
    materialTexture: 1,
    materialFile: {},
    materialYear: "",
  },
  loading: false,
  rules: {
    materialName: [
      { required: true, message: "资料名称不能为空", trigger: "blur" },
    ],
    // materialCluster: [
    //   { required: true, message: "资料所属馆藏不能为空", trigger: "blur" },
    // ],
    materialCategory: [
      { required: true, message: "资料所属类目不能为空", trigger: "blur" },
    ],
    materialTexture: [
      { required: true, message: "资料质地不能为空", trigger: "blur" },
    ],
    materialFileList: [
      { required: true, message: "资料文件不能为空", trigger: "change" },
    ],
    // materialType: [
    //   { required: true, message: "资料类型不能为空", trigger: "blur" },
    // ],
    year: [{ required: true, message: "发布时间不能为空", trigger: "blur" }],
    materialStock: [
      { required: true, message: "资料库存不能为空", trigger: "change" },
    ],
  },
});
const { form, rules, loading } = toRefs(data);
const queryParams = ref({
  current: 1,
  size: 10,
  materialName: undefined,
  materialCategory: undefined,
  materialYear: undefined,
  createTimeStart: [],
  orderBy: "",
  sortType: "",
});
/** 查询部门下拉树结构 */
function getTree() {
  getCategoryTree({
    categoryType: 1,
  }).then((response) => {
    categoryOptions.value = response.data;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.orderBy = "";
  queryParams.value.sortType = "";
  queryParams.value.current = 1;
  getList();
}
function handleReset() {
  queryParams.value = {
    current: 1,
    size: 10,
    materialName: undefined,
    materialCategory: undefined,
    materialYear: undefined,
    createTimeStart: [],
  };
  handleQuery();
}
/** 查询用户列表 */
function getList() {
  loading.value = true;
  let {
    current,
    size,
    materialName,
    materialCategory,
    materialYear,
    createTimeStart,
    orderBy,
    sortType,
  } = queryParams.value;
  let params = {
    current,
    size,
    materialName,
    orderBy,
    sortType,
    materialCategory: materialCategory
      ? materialCategory[materialCategory.length - 1].toString()
      : "",
    materialYear,
    createTimeStart: createTimeStart[0] || "",
    createTimeEnd: createTimeStart[1] || "",
  };

  const queryApi = {
    1: getElectronicBookList,
    2: statisticSelectronic,
  }[pageType.value];

  queryApi(params)
    .then((res) => {
      bookList.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
const sortChange = (column) => {
  console.log(column, "column");

  (queryParams.value.orderBy = column.prop),
    (queryParams.value.sortType = { descending: "DESC", ascending: "ASC" }[
      column.order
    ]),
    getList();
};
const fileUploadRef = ref(null);
function handleOpen(flag, item) {
  console.log(item, "item");
  open.value = true;
  if (flag === 0) {
    status.value = flag;
    title.value = "编辑资料";
    let { id, categoryNames, materialCategory, materialName, materialFile } =
      item;
    form.value = {
      id,
      categoryNames,
      materialCategory,
      materialName,
      materialTexture: 1,
      materialFile,
      materialFileList: [materialFile],
      materialYear: String(item.materialYear),
    };
    nextTick(() => {
      fileUploadRef.value.fileList = [];
      let fileList = [item.materialFile];
      fileList.forEach((item) => {
        fileUploadRef.value.fileList.push({
          name: item.fileName,
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileSuffix: item.fileSuffix,
          fileUrl: item.fileUrl,
        });
      });
    });
  } else if (flag === 1) {
    status.value = flag;
    title.value = "录入资料";
    form.value = {
      materialTexture: 1,
      materialFile: {},
    };
  } else if (flag === 2) {
    status.value = flag;
    title.value = "查看资料";
    form.value = {
      ...item,
      materialPath: item?.materialFile?.fileUrl,
      materialYear: String(item.materialYear),
    };
  }
  if (form.value.icon) form.value.iconList = [form.value.icon];
  console.log(form.value);
}
function addBook() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const api = status.value === 1 ? addCategoryBook : updateCategoryBook;
      let params = deepClone(form.value);
      if (params.iconList && params.iconList.length > 0)
        params.icon = params.iconList[0];
      if (params.materialFileList && params.materialFileList.length > 0)
        params.materialFile = params.materialFileList[0];
      if (params.materialPath && params.materialType == 1) {
        params.materialPath = "";
      } else {
        params.materialFile;
      }

      // console.log(params);
      // return;
      api(params)
        .then(() => {
          getList();

          ElMessage({
            message: "保存成功",
            type: "success",
            duration: 3 * 1000,
          });
          open.value = false;
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
function deleteBook(row = {}) {
  const idsStr = row.id || ids.value.join(",");
  proxy.$modal
    .confirm("是否确认删除选择的数据项?")
    .then(() => {
      proxy.$modal.loading();
      return deleteCategoryBook(idsStr);
    })
    .then(() => {
      getList();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function bookTypeName(id) {
  return bookTypeList.value.filter((item) => {
    if (item.id == id) return true;
  })[0]?.label;
}
/** 导出按钮操作 */
function handleExport() {
  let {
    current,
    size,
    materialName,
    materialCategory,
    materialYear,
    createTimeStart,
  } = queryParams.value;
  let params = {
    current,
    size,
    materialName,
    materialCategory: materialCategory
      ? materialCategory[materialCategory.length - 1].toString()
      : "",
    materialYear,
    createTimeStart: createTimeStart[0] || "",
    createTimeEnd: createTimeStart[1] || "",
  };
  exportMaterialElectronic(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `电子资料清单${new Date().getTime()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    proxy.$message.success("导出成功");
  });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
getTree();
getList();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .catemana-warp {
    display: flex;
    flex-direction: row;
    .catemana-table {
      flex: 1 1 auto;
      min-height: calc(100vh - 230px);
      :deep(.el-form-item) {
        width: 100%;
      }
      .catemana-handle {
        display: flex;
        padding: 10px 0 20px 0;
        .handle-item {
          margin-right: 20px;
        }
      }
    }
  }
  .catemana-dialog {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 20px;
    width: 100%;
    .catemana-dialog-left {
      width: 400px;
    }
    .catemana-dialog-right {
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }
      .avatar-uploader .el-upload:hover {
        border-color: #409eff;
      }
      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
      }
      .avatar {
        width: 178px;
        height: 178px;
        display: block;
      }
    }
  }
}
</style>
