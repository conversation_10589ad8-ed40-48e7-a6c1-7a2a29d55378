<template>
  <div class="home-container">
    <!-- <CarouselCard title="资料概览设置：">
      <div class="input-item">
        <el-form-item label="当前电子资料总数:" prop="name">
          <el-input
            v-model="dataView.realBookCount"
            placeholder="请输入电子资料总数"
            clearable
            disabled
          />
        </el-form-item>
        <el-form-item label="设置电子资料总数:" prop="name">
          <el-input
            v-model="dataView.bookCount"
            placeholder="请输入电子资料总数"
            clearable
          />
        </el-form-item>
        <el-form-item label="当前影音资料总数:" prop="name">
          <el-input
            v-model="dataView.realBookCount"
            placeholder="请输入影音资料总数"
            clearable
            disabled
          />
        </el-form-item>
        <el-form-item label="设置影音资料总数:" prop="name">
          <el-input
            v-model="dataView.materialCount"
            placeholder="请输入影音资料总数"
            clearable
          />
        </el-form-item>
      </div>
    </CarouselCard>
    <div class="home-btn">
      <el-button type="primary" @click="handleDataUpdate">保存</el-button>
    </div> -->
    <CarouselCard title="资料类目设置：">
      <CategoryUpload></CategoryUpload>
    </CarouselCard>
  </div>
</template>
<script setup name="Category">
import CategoryUpload from "@/views/admin/electronics/components/CategoryUpload.vue";
import CarouselCard from "@/views/admin/home/<USER>/CarouselCard.vue";
// import { getDataViewer, setDataViewer } from "@/api/admin/electronics.js";
// import { ElMessage } from "element-plus";

// const { proxy } = getCurrentInstance();
// const dataView = ref({
//   bookCount: 0,
//   materialCount: 0,
//   realBookCount: 0,
//   realMaterialCount: 0,
// });
// function getDataViewerData() {
//   proxy.$modal.loading();

//   getDataViewer()
//     .then((res) => {
//       dataView.value = { ...res.data };
//     })
//     .finally(() => {
//       proxy.$modal.closeLoading();
//     });
// }
// function handleDataUpdate() {
//   proxy.$modal.loading();

//   setDataViewer({
//     bookCount: parseInt(dataView.value.bookCount),
//     materialCount: parseInt(dataView.value.materialCount),
//   })
//     .then((res) => {
//       getDataViewerData();

//       ElMessage({
//         message: "保存成功",
//         type: "success",
//         duration: 3 * 1000,
//       });
//     })
//     .finally(() => {
//       proxy.$modal.closeLoading();
//     });
// }
// getDataViewerData();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .input-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .el-form-item {
      width: 49%;
    }
  }
  .home-btn {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
