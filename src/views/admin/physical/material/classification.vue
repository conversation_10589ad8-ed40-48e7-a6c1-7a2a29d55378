<template>
  <div class="home-container">
    <CarouselCard title="资料类目设置：">
      <CategoryUpload :categoryType="2"></CategoryUpload>
    </CarouselCard>
  </div>
</template>
<script setup name="Category">
import CategoryUpload from "@/views/admin/electronics/components/CategoryUpload.vue";
import CarouselCard from "@/views/admin/home/<USER>/CarouselCard.vue";
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .input-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .el-form-item {
      width: 49%;
    }
  }
  .home-btn {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
