<template>
  <div class="app-container">
    <div>
      <div class="v-title v-m-y-20">资料概览设置</div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="150px" style="width: 80%; padding: 20px 60px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当前实体资料总数">
              <el-input v-model="realTotalNum" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设置实体资料总数" prop="totalNum">
              <el-input v-model="form.totalNum" placeholder="请输入实体资料总数" @input="value => form.totalNum = floatNumber(value, 0)" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="借阅说明" prop="borrowExplain">
              <el-input v-model="form.borrowExplain" placeholder="请输入借阅说明" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="时间戳" prop="dateExplain">
              <el-input v-model="form.dateExplain" placeholder="例如：夏季、冬季"  />
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="" label-width="0" prop="time">
              <el-time-picker 
                v-model="form.time" 
                format="HH:mm"
                value-format="HH:mm"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="submitForm" style="margin: 20px auto;">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup name='MaterialIndex'>
import { materialSet, materialSetEdit } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();

const realTotalNum = ref(0);
const data = reactive({
  form: {
    totalNum: "",
    borrowExplain: "",
    dateExplain: "",
    time: [],
  },
  rules: {
    borrowExplain: [
      { required: true, message: "请输入借阅说明", trigger: "blur" }
    ],
    dateExplain: [
      { required: true, message: "请输入时间戳", trigger: "blur" }
    ],
    time: [
      { required: true, message: "请选择时间", trigger: "change" }
    ],
  }
});

const { form, rules, } = toRefs(data);

async function getSet() {
  const { data } = await materialSet();
  realTotalNum.value = data.realTotalNum || 0;
  form.value = {
    totalNum: data.totalNum || "",
    borrowExplain: data.borrowExplain || "",
    dateExplain: data.dateExplain || "",
    time: [],
  }
  if (data.openHour && data.openMinute && data.closeHour && data.closeMinute) {
    form.value.time = [
      data.openHour + ":" + data.openMinute,
      data.closeHour + ":" + data.closeMinute,
    ];
  }
}

function submitForm() {
  proxy.$refs.formRef.validate(async valid => {
    if (!valid) return 
    let params = {
      totalNum: form.value.totalNum,
      borrowExplain: form.value.borrowExplain,
      dateExplain: form.value.dateExplain,
      openHour: form.value.time[0].split(":")[0],
      openMinute: form.value.time[0].split(":")[1],
      closeHour: form.value.time[1].split(":")[0],
      closeMinute: form.value.time[1].split(":")[1],
    }
    await materialSetEdit(params);
    proxy.$message.success("操作成功");
    getSet();
  })
}

getSet();
</script>
