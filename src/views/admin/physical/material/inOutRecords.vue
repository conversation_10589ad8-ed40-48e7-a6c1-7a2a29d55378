<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="年份" prop="materialYear">
        <el-date-picker
          type="year"
          v-model="queryParams.materialYear"
          value-format="YYYY"
          placeholder="请选择年份"
        />
      </el-form-item>
      <!-- <el-form-item label="出入库类型" prop="recordType">
        <el-select
          v-model="queryParams.recordType"
          placeholder="请选择出入库类型"
          clearable
        >
          <el-option
            v-for="dict in physical_inout_types"
            :key="dict.id"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item
        :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '时间'"
        prop="date"
      >
        <el-date-picker
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="queryParams.date"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-popover
          placement="top-start"
          title=""
          :width="430"
          trigger="hover"
          content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
        >
          <template #reference>
            <el-button type="info" plain icon="Download" @click="handleExport"
              >导出资料清单</el-button
            >
          </template>
        </el-popover>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="资料名称" prop="materialName" align="center" />
      <el-table-column label="书目编号" prop="materialNo" align="center" />

      <el-table-column
        v-if="['3'].includes(pageType)"
        label="分类 —— 类目 —— 子类"
        align="center"
      >
        <template #default="scope">
          {{ replaceStr(scope.row.categoryNames) }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="['3'].includes(pageType)"
        label="印刷时间"
        width="120"
        prop="printData"
        align="center"
      />

      <el-table-column
        :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '时间'"
        prop="recordDate"
        width="120"
        align="center"
      />
      <el-table-column
        width="90"
        :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '数量'"
        align="center"
      >
        <template #default="{ row }">
          {{ Math.abs(row.stockNum) || 0 }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="['2', '3'].includes(pageType)"
        :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '对象'"
        prop="outTarget"
        align="center"
      />

      <el-table-column label="操作" align="center" width="160">
        <template #default="{ row }">
          <el-link type="primary" :underline="false" @click="handleDetail(row)"
            >查看</el-link
          >
          <el-link
            class="v-m-x-10"
            type="primary"
            :underline="false"
            @click="handleEdit(row)"
            >编辑</el-link
          >
          <el-link type="primary" :underline="false" @click="handleDelete(row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <inOutDetail
      ref="inOutDetailRef"
      :pageType="pageType"
      :physical_inout_types="physical_inout_types"
    />
    <inForm
      ref="inFormRef"
      :electronic_cluster_type="electronic_cluster_type"
      :pageType="pageType"
      dialogTitle="编辑"
      @getList="getList"
    />
    <outForm
      ref="outFormRef"
      :pageType="pageType"
      dialogTitle="编辑"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <deliveryForm
      ref="deliveryFormRef"
      :pageType="pageType"
      dialogTitle="编辑"
      :categorysArr="categorysArr"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
  </div>
</template>

<script setup name="InOutRecords">
import { getRecordList } from "@/api/admin/godown";
import {
  stockRecordInDel,
  stockRecordOutDel,
  stockRecordDispatchDel,
  dispatchListExport,
  inListExport,
  outListExport,
} from "@/api/admin/physical";
import inOutDetail from "./components/inOutDetail.vue";
import { replaceStr } from "@/utils/validate";
import inForm from "./components/inForm.vue";
import outForm from "./components/outForm.vue";
import deliveryForm from "./components/deliveryForm.vue";
const { proxy } = getCurrentInstance();
const { physical_inout_types, electronic_cluster_type } = proxy.useDict(
  "physical_inout_types",
  "electronic_cluster_type"
);

const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    recordTypeList: [1], //出入库类型 1-入库 2-出库  3-派送出库
    materialName: "",
    materialYear: "",
    date: [],
  },
  tableData: [],
});
const route = useRoute();
const pageType = computed(() => {
  // 1-入库管理 2-出库管理 3-派送管理
  return route.query.pageType ?? 1;
});

const { queryParams, tableData } = toRefs(data);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  let params = {
    ...queryParams.value,
    recordTypeList: [pageType.value],
  };
  if (params.date && params.date.length) {
    params.stockStartDate = params.date[0];
    params.stockEndDate = params.date[1];
  }

  delete params.date;
  const { data } = await getRecordList(params);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

function handleDetail(row) {
  proxy.$refs.inOutDetailRef.showDialog(row);
}

function handleEdit(row) {
  const dialog = { 1: "inFormRef", 2: "outFormRef", 3: "deliveryFormRef" }[
    pageType.value
  ];
  proxy.$refs[dialog].showDialog(row);
}

function handleDelete(row) {
  const queryApi = {
    1: stockRecordInDel,
    2: stockRecordOutDel,
    3: stockRecordDispatchDel,
  }[pageType.value];
  proxy
    .$confirm("确认删除吗？", "提示")
    .then(async () => {
      await queryApi({
        recordCode: row.recordCode,
      });
      proxy.$message.success("操作成功");
      getList();
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  let params = {
    ...queryParams.value,
    recordTypeList: [pageType.value],
  };
  if (params.date && params.date.length) {
    params.stockStartDate = params.date[0];
    params.stockEndDate = params.date[1];
  }
  delete params.date;

  const exportApi = {
    1: inListExport,
    2: outListExport,
    3: dispatchListExport,
  }[pageType.value];

  exportApi(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `${
        { 1: "入库管理", 2: "出库管理", 3: "派送管理" }[pageType.value]
      }${new Date().getTime()}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    // proxy.$message.success("导出成功");
  });
}

getList();
</script>
