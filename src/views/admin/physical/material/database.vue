<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="书目编号" prop="materialNo">
        <el-input
          v-model="queryParams.materialNo"
          placeholder="请输入书目编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="分类类目子类" prop="materialCategory">
        <el-cascader
          v-model="queryParams.materialCategory"
          :options="classifyList"
          :props="classifyProps"
        />
      </el-form-item>
      <!-- <el-form-item label="库存" prop="materialStock">
        <el-input
          v-model="queryParams.materialStock"
          placeholder="请输入库存"
          clearable
          @input="value => queryParams.materialStock = floatNumber(value, 0)"
        />
      </el-form-item> -->
      <!-- <el-form-item label="年份" prop="materialYear">
        <el-date-picker
          type="year"
          v-model="queryParams.materialYear"
          value-format="YYYY"
          placeholder="请选择年份"
        />
      </el-form-item> -->

      <!-- <el-form-item label="状态" prop="materialStatus">
        <el-select
          v-model="queryParams.materialStatus"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="dict in physical_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新建资料</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-popover
          placement="top-start"
          title=""
          :width="430"
          trigger="hover"
          content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
        >
          <template #reference>
            <el-button type="info" plain icon="Download" @click="handleExport"
              >导出资料清单</el-button
            >
          </template>
        </el-popover>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column
        label="资料名称"
        prop="materialName"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column label="书目编号" prop="materialNo" align="center" />
      <!-- <el-table-column
        label="分类 —— 类目 —— 子类"
        align="center"
        prop="categoryNames"
      >
        <template #default="scope">
          {{ replaceStr(scope.row.categoryNames) }}
        </template>
      </el-table-column>
      <el-table-column label="印刷时间" prop="printDate" align="center" /> -->
      <!-- <el-table-column label="年份" prop="materialYear" align="center" /> -->
      <el-table-column label="印刷时间" prop="printDate" align="center" />

      <el-table-column label="派送总数" prop="dispatchStock" align="center" />
      <el-table-column label="入库总数" prop="inStock" align="center" />
      <el-table-column label="出库总数" prop="outStock" align="center" />

      <el-table-column label="库存总数" prop="curStock" align="center" />

      <el-table-column label="余量总数" prop="curRemainStock" align="center" />
      <!-- <el-table-column label="状态" align="center">
        <template #default="{ row }">
          <dict-tag :options="physical_status" :value="row.materialStatus" />
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li style="color: #909399" @click="handleDetail(row)">
              <el-icon><View /></el-icon>查看
            </li>
            <li style="color: #409eff" @click="handleEdit(row)">
              <el-icon><EditPen /></el-icon>编辑
            </li>
            <li style="color: #67c23a" @click="handlePrinting(row)">
              <el-icon><DocumentAdd /></el-icon>增印
            </li>
            <li style="color: #67c23a" @click="handleIn(row)">
              <el-icon><FirstAidKit /></el-icon>入库
            </li>
            <li style="color: #e6a23c" @click="handleOut(row)">
              <el-icon><Suitcase /></el-icon>出库
            </li>
            <li style="color: #e6a23c" @click="handleDelivery(row)">
              <el-icon><Bicycle /></el-icon>派送
            </li>
            <li style="color: #f56c6c" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>删除
            </li>

            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <el-tag size="large" class="v-m-y-10" type="primary"
      >资料总数合计：{{ materialStock }}</el-tag
    >
    <el-tag size="large" class="v-m-y-10 v-m-x-10" type="primary"
      >库存总数合计：{{ curStock }}</el-tag
    >
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <dataForm
      ref="dataFormRef"
      :categorysArr="categorysArr"
      :electronic_cluster_type="electronic_cluster_type"
      :physical_status="physical_status"
      @getList="getList"
    />
    <dataDetail
      ref="dataDetailRef"
      :categorysArr="categorysArr"
      :electronic_cluster_type="electronic_cluster_type"
      :physical_status="physical_status"
    />
    <inForm
      ref="inFormRef"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <outForm
      ref="outFormRef"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <printingForm
      ref="printingFormRef"
      :categorysArr="categorysArr"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <deliveryForm
      ref="deliveryFormRef"
      :categorysArr="categorysArr"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />

    <!-- 用户导入对话框 -->
    <dialogFullScreen
      :title="uploadDialog.title"
      v-model="uploadDialog.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="uploadDialog.headers"
        :action="uploadDialog.url"
        :disabled="uploadDialog.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="uploadDialog.open = false">取 消</el-button>
        </div>
      </template>
    </dialogFullScreen>
  </div>
</template>

<script setup name="Database">
import dataForm from "./components/dataForm.vue";
import printingForm from "./components/printingForm.vue";
import deliveryForm from "./components/deliveryForm.vue";
import dataDetail from "./components/dataDetail.vue";
import inForm from "./components/inForm.vue";
import outForm from "./components/outForm.vue";
import { getToken } from "@/utils/auth";
import { replaceStr } from "@/utils/validate";
const { proxy } = getCurrentInstance();
const { electronic_cluster_type, physical_status } = proxy.useDict(
  "electronic_cluster_type",
  "physical_status"
);
import { categoryPageList, materialNumStats } from "@/api/admin/physical";
import {
  deleteCategoryBook,
  getPhysicalPageList,
  getCategoryTree,
  exportMaterialPhysical,
} from "@/api/admin/electronics";

const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const classifyProps = {
  expandTrigger: "hover",
  value: "id",
};
const getListCategory = async (categoryType) => {
  const { data } = await getCategoryTree({
    categoryType, //类目类型  1-电子资料  2-实体图书
  });
  classifyList.value = data;
};
//资料分类
const classifyList = ref([]);
const queryParams = ref({
  current: 1,
  size: 10,
  materialName: "",
  materialNo: "",
  materialCategory: "",
});
const tableData = ref([]);
const categorysArr = ref([]);
const materialStock = ref(0);
const curStock = ref(0);
const ids = ref([]);
const multiple = ref(true);

/*** 资料导入参数 */
const uploadDialog = reactive({
  // 是否显示弹出层（资料导入）
  open: false,
  // 弹出层标题（资料导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/material/physical/excel/import",
});

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getMaterialStock(params) {
  const { data } = await materialNumStats(params);
  materialStock.value = data.materialStock;
  curStock.value = data.curStock;
}

async function getList() {
  let { current, size, materialName, materialNo, materialCategory } =
    queryParams.value;
  console.log(materialCategory);
  let params = {
    current,
    size,
    materialNo,
    materialName,
    materialCategory: materialCategory
      ? materialCategory[materialCategory.length - 1].toString()
      : "",
  };
  getMaterialStock(params);
  const { data } = await getPhysicalPageList(params);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

async function getCategorysArr() {
  const { data } = await categoryPageList({
    current: 1,
    size: 10,
    categoryType: 2,
  });
  categorysArr.value = data.records || [];
}

function handleAdd() {
  proxy.$refs.dataFormRef.showDialog("add");
}

function handleEdit(row) {
  proxy.$refs.dataFormRef.showDialog("edit", row);
}

function handleDetail(row) {
  proxy.$refs.dataDetailRef.showDialog(row);
}

function handleIn(row) {
  proxy.$refs.inFormRef.showDialog(row);
}

function handleOut(row) {
  proxy.$refs.outFormRef.showDialog(row);
}
function handlePrinting(row) {
  proxy.$refs.printingFormRef.showDialog(row);
}
function handleDelivery(row) {
  proxy.$refs.deliveryFormRef.showDialog(row);
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  // single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleDelete(row) {
  // proxy
  //   .$confirm("确认删除吗？", "提示")
  //   .then(async () => {
  //     await deleteCategoryBook(row.id);
  //     proxy.$message.success("操作成功");
  //     getList();
  //   })
  //   .catch(() => {});

  const idsStr = row.id || ids.value.join(",");
  proxy.$modal
    .confirm("是否确认删除选择的数据项?")
    .then(() => {
      proxy.$modal.loading();
      return deleteCategoryBook(idsStr);
    })
    .then(() => {
      getList();
      proxy.$message.success("操作成功");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

/** 导入按钮操作 */
function handleImport() {
  uploadDialog.title = "资料导入";
  uploadDialog.open = true;
}
/** 导出按钮操作 */
function handleExport() {
  let { current, size, materialName, materialNo, materialCategory } =
    queryParams.value;
  console.log(materialCategory);
  let params = {
    current,
    size,
    materialNo,
    materialName,
    materialCategory: materialCategory
      ? materialCategory[materialCategory.length - 1].toString()
      : "",
  };
  exportMaterialPhysical(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `实体资料清单${new Date().getTime()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    proxy.$message.success("导出成功");
  });
}
/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "material/physical/tmpl-download",
    {},
    `实体资料导入模板_${new Date().getTime()}.xlsx`
  );
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadDialog.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadDialog.open = false;
  uploadDialog.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    `<div class="v-m-10" style="overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 6px 12px 0;">
    ${response.data.failNum === 0 ? "导入成功！" : response.data.failReason}
  </div>`,
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

getList();

getCategorysArr();
getListCategory(2);
</script>
