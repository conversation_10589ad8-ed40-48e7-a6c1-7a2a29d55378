<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <div class="v-m-x-20">
      <div class="v-title v-m-y-10">资料详情</div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="资料名称">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="年份">{{
        detailObj.materialYear || ""
      }}</el-descriptions-item> -->
        <el-descriptions-item label="资料类目">
          {{
            // categorysArr?.find((val) => val.id == detailObj.materialCategory)
            //   ?.categoryName ?? ""
            detailObj.materialCategoryName || ""
          }}
        </el-descriptions-item>
        <el-descriptions-item label="印刷时间">
          {{ detailObj.printDate }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="馆藏分类">
        <dict-tag
          :options="electronic_cluster_type"
          :value="detailObj.materialCluster"
        />
      </el-descriptions-item> -->
        <el-descriptions-item label="资料状态">
          <dict-tag
            :options="physical_status"
            :value="detailObj.materialStatus"
          />
        </el-descriptions-item>
        <el-descriptions-item label="资料总数">{{
          detailObj.materialStock
        }}</el-descriptions-item>
        <el-descriptions-item label="当前库存">{{
          detailObj.curStock
        }}</el-descriptions-item>
        <el-descriptions-item label="派送总数">{{
          detailObj.dispatchStock
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="借出量">{{
        detailObj.lendStock
      }}</el-descriptions-item>
      <el-descriptions-item label="异常量">{{
        detailObj.abnormalStock
      }}</el-descriptions-item> -->
      </el-descriptions>

      <div class="v-title v-m-y-10">当前库存</div>
      <el-table border :data="detailObj.stocks">
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <el-table-column label="书架名称" prop="shelfName" align="center" />
        <el-table-column label="资料数量" prop="realStock" align="center" />
      </el-table>

      <div class="v-title v-m-y-10">派送清单</div>
      <el-table border :data="detailObj.dispatchList">
        <el-table-column
          label="派送对象"
          align="center"
          prop="dispatchTarget"
        />
        <el-table-column label="派送时间" prop="dispatchDate" align="center" />
        <el-table-column label="资料数量" prop="dispatchNum" align="center" />
      </el-table>
    </div>

    <template #footer>
      <div>
        <el-button type="primary" @click="cancel">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="DataDetail">
import { materialDetail } from "@/api/admin/physical";
const props = defineProps({
  categorysArr: {
    type: Array,
    default: () => [],
  },
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  physical_status: {
    type: Array,
    default: () => [],
  },
});
const title = ref("");
const dialogVisible = ref(false);
const detailObj = ref({});
async function showDialog(row = {}) {
  console.log(row);
  title.value = row.materialName;
  const { data } = await materialDetail(row.id);
  detailObj.value = data || {};
  dialogVisible.value = true;
  detailObj.value.dispatchStock = row.dispatchStock;
  detailObj.value.printDate = row.printDate;
}
function cancel() {
  dialogVisible.value = false;
}
defineExpose({
  showDialog,
});
</script>
