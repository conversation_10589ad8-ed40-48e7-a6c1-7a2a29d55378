<template>
  <dialogFullScreen
    title="派送出库"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <div class="v-m-20">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="资料名称" label-class-name="label">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="印刷时间" label-class-name="label">{{
          detailObj.printDate || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="余量总数" label-class-name="label">{{
          detailObj.curRemainStock || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="资料类目" label-class-name="label">
          <!-- {{
            ["3"].includes(pageType)
              ? detailObj.categoryNames
              : categorysArr?.find(
                  (val) => val.id == detailObj.materialCategory
                )?.categoryName || ""
          }} -->
          {{ detailObj.categoryNames ?? "" }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="馆藏分类" label-class-name="label">
        <dict-tag
          :options="electronic_cluster_type"
          :value="detailObj.materialCluster"
        />
      </el-descriptions-item> -->

        <!-- <el-descriptions-item label="借出量">{{
        detailObj.lendStock
      }}</el-descriptions-item>
      <el-descriptions-item label="异常量">{{
        detailObj.abnormalStock
      }}</el-descriptions-item> -->
      </el-descriptions>
      <div class="v-m-y-20">
        {{
          ["3"].includes(pageType)
            ? "编辑说明：若增大派送数量，数量需小于当前余量总数。"
            : "派送说明：余量总数资料，通过派送发送给接收主体，减少余量总数。"
        }}
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派送对象" prop="outTarget">
              <el-input v-model="form.outTarget" placeholder="请输入派送对象" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派送数量" prop="recordStockNum">
              <el-input
                v-model="form.recordStockNum"
                placeholder="请输入派送数量"
                @input="
                  (value) => (form.recordStockNum = floatNumber(value, 0))
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="recordDate" label="派送时间">
              <el-date-picker
                v-model="form.recordDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="请输入派送时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="派送说明" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                placeholder="请输入派送说明"
                clearable
                :autosize="{ minRows: 5, maxRows: 10 }"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="deliveryForm">
import {
  deliveryPage,
  srockRecords,
  stockRecordDispatchEdit,
} from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  categorysArr: {
    type: Array,
    default: () => [],
  },
  dialogTitle: {
    type: String,
    default: "派送出库",
  },
  pageType: {
    type: String,
    default: "",
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});
const form = reactive({
  recordStockNum: undefined,
});
const rules = reactive({
  recordStockNum: [
    { required: true, message: "请输入派送量", trigger: "blur" },
  ],
  outTarget: [{ required: true, message: "请输入派送对象", trigger: "blur" }],
  recordDate: [{ required: true, message: "请选择派送数量", trigger: "blur" }],
});

function showDialog(row = {}) {
  detailObj.value = {
    ...row,
  };
  form.recordStockNum = row.recordStockNum || row.dispatchStock;
  form.outTarget = row.outTarget;
  form.recordDate = row.recordDate;
  form.remark = row.remark;
  proxy.resetForm("formRef");
  dialogVisible.value = true;
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;
    let params = {
      dispatchNum: form.recordStockNum,
      dispatchTarget: form.outTarget,
      dispatchDate: form.recordDate,
      dispatchRemark: form.remark,
    };
    let queryApi = ["3"].includes(props.pageType)
      ? stockRecordDispatchEdit
      : deliveryPage;
    if (["3"].includes(props.pageType)) {
      params.id = detailObj.value.id;
      params.materialId = detailObj.value.materialId;
    } else {
      params.materialId = detailObj.value.id;
    }

    await queryApi(params);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
