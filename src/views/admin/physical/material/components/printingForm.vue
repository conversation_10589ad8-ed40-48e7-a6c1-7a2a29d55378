<template>
  <dialogFullScreen
    title="增印编辑"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-descriptions :column="3" border>
      <el-descriptions-item label="资料名称" label-class-name="label">{{
        detailObj.materialName || ""
      }}</el-descriptions-item>
      <el-descriptions-item label="书目编号" label-class-name="label">{{
        detailObj.materialNo || ""
      }}</el-descriptions-item>
     <!--  <el-descriptions-item label="年份" label-class-name="label">{{
        detailObj.materialYear || ""
      }}</el-descriptions-item> -->
      <el-descriptions-item label="资料类目" label-class-name="label">
        {{
            categorysArr?.find((val) => val.id == detailObj.materialCategory)
              ?.categoryName || ""
        }}
      </el-descriptions-item>
      <el-descriptions-item label="资料总数">{{
        detailObj.materialStock
      }}</el-descriptions-item>
      <el-descriptions-item label="当前库存">{{
        detailObj.curStock
      }}</el-descriptions-item>
      <el-descriptions-item label="派送总数">{{
        detailObj.dispatchStock
      }}</el-descriptions-item>
      <!-- <el-descriptions-item label="借出量">{{
        detailObj.lendStock
      }}</el-descriptions-item>
      <el-descriptions-item label="异常量">{{
        detailObj.abnormalStock
      }}</el-descriptions-item> -->
      <!-- <el-descriptions-item label="馆藏分类" label-class-name="label">
        <dict-tag
          :options="electronic_cluster_type"
          :value="detailObj.materialCluster"
        />
      </el-descriptions-item> -->
    </el-descriptions>
    <div class="v-m-y-20">
      增印：已有资料通过印刷厂定制等方式增加资料数量，并未入馆，入馆以入库为主。
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="增印数量" prop="reprintNum">
            <el-input
              v-model="form.reprintNum"
              placeholder="请输入增印数量"
              @input="(value) => (form.reprintNum = floatNumber(value, 0))"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="printingForm">
import { reprintPage } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  categorysArr: {
    type: Array,
    default: () => [],
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});
const form = reactive({
  reprintNum: undefined,
});
const rules = reactive({
  reprintNum: [{ required: true, message: "请输入增印数量", trigger: "blur" }],
});

function showDialog(row = {}) {
  detailObj.value = row;
  proxy.resetForm("formRef");
  dialogVisible.value = true;
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;
    await reprintPage({
      materialId: detailObj.value.id,
      reprintNum: form.reprintNum,
    });
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
