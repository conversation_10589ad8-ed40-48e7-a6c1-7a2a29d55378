<template>
  <dialogFullScreen
    :title="dialogTitle"
    v-model="dialogVisible"
    width="80%"
    append-to-body
  >
    <div class="v-m-20">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="资料名称" label-class-name="label">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="库存总数">{{
          detailObj.curStock
        }}</el-descriptions-item>
      </el-descriptions>
      <!-- <div class="v-title v-m-y-20">选择出库</div> -->
      <div class="v-m-y-20">出库说明：库存出库数量，需要小于等于库存总数。</div>

      <el-form ref="formRef" :model="form" label-width="100px">
        <el-row :gutter="20" v-for="(item, index) in form.domains" :key="index">
          <el-col :span="6">
            <el-form-item
              label="所属仓库:"
              :prop="'domains.' + index + '.warehouseId'"
            >
              {{ item.warehouseName || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="所在书架:"
              :prop="'domains.' + index + '.shelfId'"
            >
              {{ item.shelfName || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item
              label="仓库编号:"
              :prop="'domains.' + index + '.warehouseShelfNo'"
            >
              {{ item.warehouseNo || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item
              label="资料数量:"
              :prop="'domains.' + index + '.realStock'"
            >
              {{ item.realStock || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item
              label="出库量:"
              :prop="'domains.' + index + '.stockNum'"
              :rules="{
                required: false,
                message: '请输入出库量',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.stockNum"
                placeholder="请输入出库量"
                @input="
                  (value) =>
                    (item.stockNum = limitNumber(value, item.realStock, 0))
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item
              v-if="!['2'].includes(pageType)"
              prop="outTarget"
              label="出库对象"
            >
              <el-input
                v-model="form.outTarget"
                placeholder="请输入出库对象"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item prop="recordDate" label="出库时间">
              <el-date-picker
                v-model="form.recordDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="请输入出库时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                placeholder="请输入备注"
                clearable
                :autosize="{ minRows: 5, maxRows: 10 }"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="OutForm">
import {
  distributionStock,
  outStock,
  stockRecordOutEdit,
} from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  dialogTitle: {
    type: String,
    default: "资料出库",
  },
  pageType: {
    type: String,
    default: "",
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});
const form = reactive({
  domains: [],
});
// const outTarget = ref("");

async function showDialog(row) {
  const id = ["2"].includes(props.pageType) ? row.materialId : row.id;
  const { data } = await distributionStock(id);
  form.domains = [];
  // outTarget.value = "";
  form.recordDate = data.recordDate;
  form.remark = data.remark;
  detailObj.value = data.materialInfo || {};
  data.stocks &&
    data.stocks.map((item) => {
      form.domains.push({
        materialId: item.materialId,
        warehouseName: item.warehouseName,
        warehouseId: item.warehouseId,
        shelfName: item.shelfName,
        shelfId: item.shelfId,
        realStock: item.realStock,
        stockNum: item.stockNum,
      });
    });
  detailObj.value.dispatchStock = row.dispatchStock;
  dialogVisible.value = true;
}

function limitNumber(value, max, decimal) {
  value = proxy.floatNumber(value, decimal, true);
  if (value > max) value = max;

  return value;
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;

    let queryApi = outStock;
    const { domains: stocks, ...rest } = form;
    let params = {
      stocks,
      ...rest,
      // outTarget: outTarget.value,
    };

    if (["1"].includes(props.pageType)) {
      queryApi = stockRecordOutEdit;
      params.recordCode = detailObj.value.recordCode;
    }

    await queryApi(params);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
