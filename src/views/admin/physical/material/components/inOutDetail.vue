<template>
  <dialogFullScreen
    :title="'查看' || title"
    v-model="dialogVisible"
    width="60%"
    append-to-body
  >
    <div class="v-m-20">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="资料名称" label-class-name="label">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>
        <el-descriptions-item
          v-if="['1', '3'].includes(pageType)"
          label="印刷时间"
          label-class-name="label"
          >{{ detailObj.printData || "" }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="['1', '3'].includes(pageType)"
          label="资料类目"
          label-class-name="label"
          >{{ detailObj.categoryNames || "" }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="['1'].includes(pageType)"
          label="余量总数总量"
          label-class-name="label"
          >{{ detailObj.curRemainStock || "" }}</el-descriptions-item
        >

        <el-descriptions-item
          v-if="['2'].includes(pageType)"
          label="库存总数"
          label-class-name="label"
          >{{ detailObj.curStock || "" }}</el-descriptions-item
        >

        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>

        <el-descriptions-item
          v-if="['2', '3'].includes(pageType)"
          :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '对象'"
          label-class-name="label"
          >{{ detailObj.outTarget || "" }}</el-descriptions-item
        >

        <el-descriptions-item
          v-if="['3'].includes(pageType)"
          :label="{ 1: '入库', 2: '出库', 3: '派送' }[pageType] + '数量'"
          label-class-name="label"
          >{{ detailObj.recordStockNum || "" }}</el-descriptions-item
        >

        <el-descriptions-item
          :label="{ 1: '备注', 2: '备注', 3: '派送说明' }[pageType]"
          label-class-name="label"
          >{{ detailObj.remark || "" }}</el-descriptions-item
        >

        <!-- <el-descriptions-item label="资料数量">{{
        detailObj.materialStock
      }}</el-descriptions-item>
      <el-descriptions-item label="当前库存">{{
        detailObj.curStock
      }}</el-descriptions-item>
      <el-descriptions-item label="派送总数">{{
        detailObj.dispatchStock
      }}</el-descriptions-item> -->
        <!-- <el-descriptions-item label="借出量">{{
        detailObj.lendStock
      }}</el-descriptions-item>
      <el-descriptions-item label="异常量">{{
        detailObj.abnormalStock
      }}</el-descriptions-item> -->
      </el-descriptions>
      <el-table
        v-if="['1', '2'].includes(pageType)"
        class="v-m-y-20"
        border
        :data="detailObj.recordList || []"
      >
        <el-table-column label="所属仓库" prop="warehouseName" align="center" />
        <el-table-column label="书架" prop="shelfName" align="center" />
        <el-table-column
          v-if="['2'].includes(pageType)"
          label="仓架编号"
          prop="warehouseShelfNo"
          align="center"
        />
        <el-table-column
          :label="{ 1: '入库', 2: '资料', 3: '派送' }[pageType] + '数量'"
          align="center"
        >
          <template #default="{ row }">
            {{ Math.abs(row.stockNum) || 0 }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button @click="cancel">关闭</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="InOutDetail">
import { srockRecords } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const props = defineProps({
  physical_inout_types: {
    type: Array,
    default: () => [],
  },
  pageType: {
    type: String,
    default: "1",
  },
});

const title = ref("");
const dialogVisible = ref(false);
const detailObj = ref({});

async function showDialog(row) {
  title.value = row.materialName;
  const { data } = await srockRecords(row.recordCode);
  detailObj.value = data || {};
  dialogVisible.value = true;
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
