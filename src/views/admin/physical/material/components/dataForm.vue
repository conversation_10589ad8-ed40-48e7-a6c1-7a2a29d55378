<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-form
      class="v-m-20"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="125px"
      style="margin-top: 20px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="资料名称" prop="materialName">
            <el-input
              v-model="form.materialName"
              placeholder="请输入资料名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="书目编号" prop="materialNo">
            <el-input v-model="form.materialNo" placeholder="请输入书目编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="印刷时间" prop="printDate">
            <el-date-picker
              type="month"
              v-model="form.printDate"
              value-format="YYYY-MM"
              placeholder="请选择印刷时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类类目子类" prop="materialCategory">
            <el-tree-select
              v-model="form.materialCategory"
              :data="categoryOptions"
              :props="{ value: 'id', label: 'label', children: 'children' }"
              value-key="id"
              placeholder="请选择资料所属类分类"
              check-strictly
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- <el-col :span="12">
          <el-form-item label="馆藏分类" prop="materialCluster">
            <el-select
              v-model="form.materialCluster"
              placeholder="请选择馆藏分类"
            >
              <el-option
                v-for="dict in props.electronic_cluster_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="资料总数" prop="materialStock">
            <el-input
              v-model="form.materialStock"
              placeholder="请输入资料总数"
              @input="(value) => (form.materialStock = floatNumber(value, 0))"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="资料类型" prop="physicalType">
            <el-select v-model="form.physicalType" placeholder="请选择资料类型">
              <el-option
                v-for="dict in [
                  { label: '对外资料', value: 2 },
                  { label: '内部资料', value: 1 },
                ]"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="DataForm">
import { getCategoryTree } from "@/api/admin/electronics";
import dayjs from "dayjs";
import {
  materialDetail,
  queryPhysicalAdd,
  queryPhysicalEdit,
} from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  categorysArr: {
    type: Array,
    default: () => [],
  },
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  physical_status: {
    type: Array,
    default: () => [],
  },
});

const title = ref("");
const dialogVisible = ref(false);
const categoryOptions = ref(undefined);
const data = reactive({
  form: {},
  rules: {
    materialName: [
      { required: true, message: "请输入资料名称", trigger: "blur" },
    ],
    materialNo: [
      { required: true, message: "请输入书目编号", trigger: "blur" },
    ],
    materialCategory: [
      { required: true, message: "请选择资料类目", trigger: "change" },
    ],
    materialCluster: [
      { required: true, message: "请选择馆藏分类", trigger: "change" },
    ],
    materialStock: [
      { required: true, message: "请输入资料数量", trigger: "blur" },
    ],
    physicalType: [
      { required: true, message: "请选择资料类型", trigger: "blur" },
    ],
  },
});

const { form, rules } = toRefs(data);

async function showDialog(type, row = {}) {
  form.value = {
    materialTexture: 2,
    materialName: "",
    materialNo: "",
    printDate: "",
    materialCategory: "",
    materialStock: "",
    materialCluster: "",
    physicalType: 2,
  };
  proxy.resetForm("formRef");
  title.value = type == "add" ? "新建资料" : "编辑资料";
  if (row && row.id) {
    const { data } = await materialDetail(row.id);
    form.value = {
      ...form.value,
      ...(data || {}),
      materialYear: data.materialYear && data.materialYear.toString(),
    };
  }
  getTree();
  dialogVisible.value = true;
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;
    let action = form.value.id ? queryPhysicalEdit : queryPhysicalAdd;
    let queryParams = {
      ...form.value,
      // printDate: dayjs(form.value.printDate).format("YYYY-MM-01"),
    };

    form.value.printDate &&
      (queryParams.printDate = dayjs(form.value.printDate).format(
        "YYYY-MM-01"
      ));
    await action(queryParams);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}

/** 查询部门下拉树结构 */
function getTree() {
  getCategoryTree({
    categoryType: 2,
  }).then((response) => {
    categoryOptions.value = response.data;
  });
}

defineExpose({
  showDialog,
});
</script>
