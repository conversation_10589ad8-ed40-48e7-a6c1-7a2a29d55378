<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" style="margin-top: 20px;">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类目名称" prop="categoryName">
            <el-input v-model="form.categoryName" placeholder="请输入类目名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="orderNum">
            <el-input
              v-model="form.orderNum" 
              placeholder="请设置优先级0-99" 
              :min="0" 
              :max="99"  
              @input="value => form.orderNum = limitNumber(value, 0)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="选择文种" prop="categoryLingual">
            <el-select 
              v-model="form.categoryLingual" 
              placeholder="请选择文种" 
              filterable
            >
              <el-option 
                v-for="dict in props.physical_record_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="选择类别" prop="categoryClass">
            <el-select 
              v-model="form.categoryClass" 
              placeholder="请选择类别" 
              filterable
            >
              <el-option 
                v-for="dict in props.physical_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <template v-if="form.id">
          <el-col :span="12">
            <el-form-item label="资料数量" prop="realCount">
              <el-input v-model="form.realCount" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="展示数量" prop="showCount">
              <el-input v-model="form.showCount" placeholder="请输入展示" @input="value => form.showCount = floatNumber(value, 0)" />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='ClassForm'>
import { categoryDetail, categoryEdit } from "@/api/admin/physical";
import { addCategoryNode } from "@/api/admin/electronics";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  physical_record_type: {
    type: Array,
    default: () => []
  },
  physical_category: {
    type: Array,
    default: () => []
  },
})

const title = ref("");
const dialogVisible = ref(false);
const data = reactive({
  form: {},
  rules: {
    categoryName: [
      { required: true, message: "请输入资料名称", trigger: "blur" }
    ],
    orderNum: [
      { required: true, message: "请设置优先级0-99", trigger: "blur" }
    ],
    // categoryLingual: [
    //   { required: true, message: "请选择文种", trigger: "change" }
    // ],
    // categoryClass: [
    //   { required: true, message: "请选择类别", trigger: "change" }
    // ],
  },
});

const { form, rules } = toRefs(data);

async function showDialog(type, row = {}) {
  form.value = {
    categoryType: 2,
    categoryName: "",
    orderNum: "",
    categoryLingual: "",
    categoryClass: "",
  };
  proxy.resetForm("formRef");
  title.value = type == "add" ? "新建类目" : "编辑类目";
  if (row && row.id) {
    const { data } = await categoryDetail(row.id);
    form.value = {
      ...form.value,
      ...data || {}
    }
  }
  dialogVisible.value = true;
}

function limitNumber(value, decimal) {
  value = proxy.floatNumber(value, decimal);
  if (value > 99) value = 99;

  return value
}

function submitForm() {
  proxy.$refs.formRef.validate(async valid => {
    if (!valid) return
    let action = form.value.id ? categoryEdit : addCategoryNode;
    await action(form.value);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  })
}

function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}

defineExpose({
  showDialog
})
</script>
