<template>
  <dialogFullScreen
    :title="dialogTitle"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <div class="v-m-20">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="资料名称" label-class-name="label">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>

        <el-descriptions-item label="印刷时间" label-class-name="label">{{
          detailObj.printDate || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="资料类目" label-class-name="label">{{
          detailObj.categoryNames || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="余量总数" label-class-name="label">{{
          detailObj.curRemainStock || ""
        }}</el-descriptions-item>

        <!--  <el-descriptions-item label="借出量">{{
          detailObj.lendStock
        }}</el-descriptions-item>
        <el-descriptions-item label="异常量">{{
          detailObj.abnormalStock
        }}</el-descriptions-item>
        <el-descriptions-item label="馆藏分类" label-class-name="label">
          <dict-tag
            :options="electronic_cluster_type"
            :value="detailObj.materialCluster"
          />
        </el-descriptions-item> -->
      </el-descriptions>
      <div class="v-m-y-20">入库：记录将资料置于具体书架，从未入库到库存。</div>
      <div class="v-m-y-20">
        <el-icon
          :size="30"
          color="#409eff"
          class="v-pointer"
          style="transform: translateY(9px)"
          @click="addRows"
        >
          <CirclePlusFilled />
        </el-icon>
        <span style="margin-left: 10px; margin-right: 50px">编辑入库</span>
      </div>
      <el-form ref="formRef" :model="form" label-width="100px">
        <el-row :gutter="20" v-for="(item, index) in form.domains" :key="index">
          <el-col :span="8">
            <el-form-item
              label="所属仓库"
              :prop="'domains.' + index + '.warehouseId'"
              :rules="{
                required: true,
                message: '请选择所属仓库',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="item.warehouseId"
                placeholder="请选择所属仓库"
                filterable
                @change="(value) => getBooks(value, item)"
              >
                <el-option
                  v-for="(item, index) in waresArr"
                  :key="index"
                  :label="item.warehouseName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="选择书架"
              :prop="'domains.' + index + '.shelfId'"
              :rules="{
                required: true,
                message: '请选择书架',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="item.shelfId"
                placeholder="请选择书架"
                filterable
              >
                <el-option
                  v-for="(item, index) in item.booksArr || []"
                  :key="index"
                  :label="item.shelfName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="['1'].includes(pageType) ? '入库数量' : '上架数量'"
              :prop="'domains.' + index + '.stockNum'"
              :rules="{
                required: true,
                message: '请输入数量',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.stockNum"
                placeholder="请输入数量"
                @input="(value) => (item.stockNum = floatNumber(value, 0))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-icon
              :size="20"
              color="#409eff"
              class="v-pointer"
              style="margin-top: 6px"
              @click="deleteRows(index)"
            >
              <RemoveFilled />
            </el-icon>
          </el-col>
        </el-row>
        <el-divider />
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="recordDate" label="入库时间">
              <el-date-picker
                v-model="form.recordDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="请输入入库时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                placeholder="请输入备注"
                clearable
                :autosize="{ minRows: 5, maxRows: 10 }"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="InForm">
import {
  warePageList,
  bookPageList,
  inStock,
  stockRecordInEdit,
  srockRecords,
} from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  dialogTitle: {
    type: String,
    default: "入库设置",
  },
  pageType: {
    type: String,
    default: "",
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});
const form = reactive({
  domains: [],
});
const waresArr = ref([]);

async function showDialog(row = {}) {
  detailObj.value = row;

  if (["1"].includes(props.pageType)) {
    const { data } = await srockRecords(row.recordCode);
    form.domains = data.recordList;
    form.recordDate = data.recordDate;
    form.remark = data.remark;
    form?.domains?.forEach((element) => {
      getBooks(element.warehouseId, element, element.shelfId);
    });
  } else {
    proxy.resetForm("formRef");
    form.domains = [];
    addRows();
  }

  dialogVisible.value = true;
}

async function getWares() {
  const { data } = await warePageList({ current: 1, size: 1000 });
  waresArr.value = data.records || [];
}

async function getBooks(value, item, shelfId = "") {
  const { data } = await bookPageList({
    current: 1,
    size: 1000,
    warehouseId: value,
  });
  item["shelfId"] = shelfId;
  item["booksArr"] = data.records || [];
}

function addRows() {
  form.domains.push({
    materialId: detailObj.value.id,
    warehouseId: "",
    shelfId: "",
    stock: "",
  });
}

function deleteRows(i) {
  form.domains.splice(i, 1);
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;
    let queryApi = inStock;
    const { domains: stocks, ...rest } = form;
    let params = {
      stocks,
      ...rest,
    };

    if (["1"].includes(props.pageType)) {
      queryApi = stockRecordInEdit;
      params.recordCode = detailObj.value.recordCode;
    }

    await queryApi(params);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});

getWares();
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
