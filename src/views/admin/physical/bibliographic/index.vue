<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="仓库名称" prop="warehouseName">
        <!-- <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        /> -->

        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择所属仓库"
          filterable
          style="width: 200px"
          @change="(value) => getShelfs(value)"
        >
          <el-option
            v-for="(item, index) in waresArr"
            :key="index"
            :label="item.warehouseName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="书架名称" prop="shelfName">
        <!-- <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入书架名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        /> -->

        <el-select
          v-model="queryParams.shelfId"
          placeholder="请选择书架"
          filterable
          style="width: 200px"
        >
          <el-option
            v-for="(item, index) in shelfArr || []"
            :key="index"
            :label="item.shelfName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:notice:add']"
          >新增</el-button
        >
      </el-col> -->

      <el-col :span="1.5" class="v-m-r-10">
        <el-tag type="primary">当前总量：{{ currentCount }}</el-tag>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="bobliographicList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="仓库名称"
        align="center"
        prop="warehouseName"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="书架名称"
        align="center"
        prop="shelfName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="仓架编号"
        align="center"
        prop="warehouseShelfNo"
        class-name="small-padding fixed-width"
      >
      </el-table-column>

      <el-table-column
        label="资料数量"
        align="center"
        prop="shelfStock"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="780px" append-to-body>
      <el-row :gutter="10" class="mb8 v-m-y-10">
        <el-col :span="1.5" class="v-m-r-10">
          当前仓库： <el-tag type="primary">{{ form.warehouseName }}</el-tag>
        </el-col>
        <el-col :span="1.5">
          当前书架：<el-tag type="primary">{{ form.shelfName }}</el-tag></el-col
        >
        <el-col :span="1.5">
          仓架编号：<el-tag type="primary">{{
            form.warehouseShelfNo
          }}</el-tag></el-col
        >
      </el-row>

      <el-table :data="form.records">
        <el-table-column
          label="资料名称"
          align="center"
          prop="materialName"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="书目编号"
          align="center"
          prop="materialNo"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="印刷时间"
          align="center"
          prop="printDate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="资料数量"
          align="center"
          prop="realStock"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancel">确 定</el-button>
          <!-- <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Bibliographic">
import {
  warePageList,
  bookPageList,
  warehousePage,
  physicalStockPage,
  physicalStockCount,
} from "@/api/admin/physical";

const { proxy } = getCurrentInstance();

const bobliographicList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const currentCount = ref(0);
const waresArr = ref([]);
const shelfArr = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    warehouseId: undefined,
    shelfId: undefined,
  },
});

const { queryParams, form } = toRefs(data);

async function getWares() {
  const { data } = await warePageList({ current: 1, size: 1000 });
  waresArr.value = data.records || [];
}

async function getShelfs(value) {
  const { data } = await bookPageList({
    current: 1,
    size: 1000,
    warehouseId: value,
  });
  queryParams.value.shelfId = "";
  shelfArr.value = data.records || [];
}

/** 查询公告列表 */
function getList() {
  loading.value = true;
  warehousePage(queryParams.value).then((response) => {
    bobliographicList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });

  physicalStockCount(queryParams.value).then((res) => {
    currentCount.value = res.data ?? 0;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    warehouseShelfNo: undefined,
    status: "0",
  };
  proxy.resetForm("noticeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.shelfId = "";
  queryParams.value.warehouseId = "";
  handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleView(row) {
  physicalStockPage({ shelfId: row.shelfId }).then((response) => {
    form.value = response.data;
    form.value.warehouseName = row.warehouseName;
    form.value.shelfName = row.shelfName;
    form.value.warehouseShelfNo = row.warehouseShelfNo;
    open.value = true;
    title.value = "查看书目";
  });
}

getList();
getWares();
</script>
