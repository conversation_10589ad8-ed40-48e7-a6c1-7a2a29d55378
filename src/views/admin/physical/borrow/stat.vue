<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="资料分类" prop="materialCategory">
        <el-cascader
          v-model="queryParams.materialCategory"
          :options="classifyList"
          :props="classifyProps"
        />
      </el-form-item>
      <el-form-item label="存在异常" prop="abnormalFlag">
        <el-select
          v-model="queryParams.abnormalFlag"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="dict in exist_abnormal_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="资料名称" prop="materialName" align="center" />
      <el-table-column label="书目编号" prop="materialNo" align="center" />
      <el-table-column
        label="分类 —— 类目 —— 子类"
        prop="categoryNames"
        align="center"
      >
        <template #default="scope">
          {{ replaceStr(scope.row.categoryNames) }}
        </template>
      </el-table-column>
      <el-table-column label="累计借阅" prop="lendStock" align="center" />
      <el-table-column label="当前借出" prop="curLendStock" align="center" />
      <el-table-column label="异常量" prop="abnormalStock" align="center" />
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li style="color: #67c23a" @click="handleDone(row)">
              <el-icon><TopLeft /></el-icon>查看异常记录
            </li>
            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <lendForm
      ref="lendFormRef"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <returnForm ref="returnFormRef" @getList="getList" />
    <borrowingStat
      ref="borrowingStatRef"
      :borrow_status="exist_abnormal_type"
      @getList="getList"
    />
  </div>
</template>

<script setup name="BorrowManage">
import { queryStatsPage } from "@/api/admin/physical";
import lendForm from "./components/lendForm.vue";
import returnForm from "./components/returnForm.vue";
import borrowingStat from "./components/borrowingStat.vue";
import { getCategoryTree } from "@/api/admin/electronics";
import { reactive } from "vue";
import { replaceStr } from "@/utils/validate";
const { proxy } = getCurrentInstance();
const route = useRoute();
const { electronic_cluster_type, exist_abnormal_type } = proxy.useDict(
  "electronic_cluster_type",
  "exist_abnormal_type"
);
console.log(exist_abnormal_type);
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const routeName = computed(() => {
  return route?.name;
});
console.log(route);
const data = reactive({
  tableData: [],
});
const classifyProps = {
  expandTrigger: "hover",
  value: "id",
};
//资料分类
const classifyList = ref([]);
const { tableData } = toRefs(data);
const queryParams = ref({
  current: 1,
  size: 10,
  materialName: "",
  materialCategory: "",
  abnormalFlag: "",
});
const getListCategory = async (categoryType) => {
  const { data } = await getCategoryTree({
    categoryType, //类目类型  1-电子资料  2-实体图书
  });
  classifyList.value = data;
};
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  let { current, size, materialName, materialCategory, abnormalFlag } =
    queryParams.value;
  console.log(materialCategory);
  let params = {
    current,
    size,
    materialName,
    abnormalFlag,
    materialCategory: materialCategory
      ? materialCategory[materialCategory.length - 1].toString()
      : "",
  };
  if (params.date && params.date.length) {
    params.borrowStartDate = params.date[0];
    params.borrowEndDate = params.date[1];
  }
  delete params.date;
  const { data } = await queryStatsPage(params);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

function handleDone(row) {
  proxy.$refs.borrowingStatRef.showDialog(row);
}
getList();
getListCategory(2);
</script>
