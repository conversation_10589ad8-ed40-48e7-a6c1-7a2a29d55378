<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="书目编号"
        prop="materialNo"
        v-if="['BorrowApply'].includes(routeName)"
      >
        <el-input
          v-model="queryParams.materialNo"
          placeholder="请输入书目编号"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="借阅人"
        prop="borrowUserName"
        v-if="['BorrowApply', 'BorrowTrack'].includes(routeName)"
      >
        <el-input
          v-model="queryParams.borrowUserName"
          placeholder="请输入借阅人"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="单位(处室)"
        prop="deptName"
        v-if="['BorrowApply'].includes(routeName)"
      >
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入单位(处室)"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="状态"
        prop="borrowStatus"
        v-if="['BorrowTrack'].includes(routeName)"
      >
        <el-select
          v-model="queryParams.borrowStatus"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="dict in borrow_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="借阅时间"
        prop="date"
        v-if="['BorrowTrack'].includes(routeName)"
      >
        <el-date-picker
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="queryParams.date"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="资料名称" prop="materialName" align="center" />
      <el-table-column label="书目编号" prop="materialNo" align="center" />
      <el-table-column label="借阅人" prop="borrowUserName" align="center" />
      <el-table-column
        label="单位(处室)"
        prop="borrowUserName"
        align="center"
        v-if="['BorrowApply', 'BorrowTrack'].includes(routeName)"
      />
      <el-table-column label="联系方式" prop="borrowUserPhone" align="center" />
      <el-table-column label="借阅时间" align="center">
        <template #default="{ row }">
          {{ row.borrowStartDate || "" }} - {{ row.borrowEndDate || "" }}
        </template>
      </el-table-column>
      <!--  <el-table-column label="馆藏分类" align="center">
        <template #default="{ row }">
          <dict-tag :options="electronic_cluster_type" :value="row.materialCluster" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="当前库存"
        prop="curStock"
        align="center"
        v-if="['BorrowApply'].includes(routeName)"
      />
      <el-table-column
        label="状态"
        align="center"
        v-if="['BorrowTrack'].includes(routeName)"
      >
        <template #default="{ row }">
          <dict-tag :options="borrow_status" :value="row.borrowStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-popover
            placement="left"
            popper-class="operate-ul"
            v-if="['BorrowApply'].includes(routeName)"
          >
            <li style="color: #409eff" @click="handleView(row)">
              <el-icon><TopLeft /></el-icon>查看
            </li>
            <li
              v-if="row.borrowStatus == 1"
              style="color: #e6a23c"
              @click="handleLend(row)"
            >
              <el-icon><TopLeft /></el-icon>借出
            </li>
            <li
              v-if="row.borrowStatus == 1"
              style="color: #f56c6c"
              @click="handleReject(row)"
            >
              <el-icon><CircleClose /></el-icon>驳回
            </li>
            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
          <el-popover
            placement="left"
            popper-class="operate-ul"
            v-if="
              !'4, 5, 6'.includes(row.borrowStatus) &&
              ['BorrowTrack'].includes(routeName)
            "
          >
            <li
              v-if="row.borrowStatus == 2"
              style="color: #409eff"
              @click="handleReturn(row)"
            >
              <el-icon><BottomRight /></el-icon>归还
            </li>
            <li
              v-if="row.borrowStatus == 3 || row.borrowStatus == 7"
              style="color: #67c23a"
              @click="handleDone(row)"
            >
              <el-icon><CircleCheck /></el-icon>办结处理
            </li>
            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <lendForm
      ref="lendFormRef"
      :electronic_cluster_type="electronic_cluster_type"
      @getList="getList"
    />
    <returnForm ref="returnFormRef" @getList="getList" />
    <completeForm
      ref="completeFormRef"
      :borrow_status="borrow_status"
      @getList="getList"
    />
    <Details ref="detailsRef" />
  </div>
</template>

<script setup name="BorrowManage">
import { borrowPage, borrowReject } from "@/api/admin/physical";
import lendForm from "./components/lendForm.vue";
import returnForm from "./components/returnForm.vue";
import completeForm from "./components/completeForm.vue";
import Details from "./components/details.vue";
const { proxy } = getCurrentInstance();
const route = useRoute();
const { electronic_cluster_type, borrow_status } = proxy.useDict(
  "electronic_cluster_type",
  "borrow_status"
);
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const routeName = computed(() => {
  return route?.name;
});
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    materialName: "",
    materialNo: "",
    borrowUserName: "",
    borrowStatus: "",
    borrowStatusList: route.name == "BorrowApply" ? [1] : [2, 3, 4, 5, 6, 7],
    date: [],
  },
  tableData: [],
});
const { queryParams, tableData } = toRefs(data);
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
async function getList() {
  let params = {
    ...queryParams.value,
  };
  if (params.date && params.date.length) {
    params.borrowStartDate = params.date[0];
    params.borrowEndDate = params.date[1];
  }
  delete params.date;
  const { data } = await borrowPage(params);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}
function handleDone(row) {
  proxy.$refs.completeFormRef.showDialog(row);
}
function handleLend(row) {
  proxy.$refs.lendFormRef.showDialog(row);
}
function handleView(row) {
  console.log("---查看---");
  proxy.$refs.detailsRef.showDialog(row);
}
function handleReturn(row) {
  proxy.$refs.returnFormRef.showDialog(row);
}
function handleReject(row) {
  proxy
    .$confirm("确认驳回借阅申请吗？", "提示")
    .then(async () => {
      await borrowReject(row.id);
      proxy.$message.success("操作成功");
      getList();
    })
    .catch(() => {});

  /* 暂存 后续可能会添加原因
  proxy.$prompt("请输入驳回原因", "驳回", {
    inputPattern: /\S/,
    inputErrorMessage: "请输入驳回原因"
  })
    .then(({ value }) => {
      // proxy.$message.success("操作成功");
    }).catch(() => {}) */
}

getList();
</script>
