<template>
  <dialogFullScreen
    title="办结处理"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-descriptions :column="3" border>
      <el-descriptions-item label="办结时间" :span="3">{{ detailObj.materialName || "" }}</el-descriptions-item>
      <el-descriptions-item label="资料名称" :span="3">{{ detailObj.materialName || "" }}</el-descriptions-item>
      <el-descriptions-item label="借阅人" :span="1">{{ detailObj.borrowUserName || "" }}</el-descriptions-item>
      <el-descriptions-item label="联系方式" :span="2">{{ detailObj.borrowUserPhone || "" }}</el-descriptions-item>
      <el-descriptions-item label="借阅时间" :span="1">
        {{ detailObj.borrowStartDate || "" }} 至 {{ detailObj.borrowEndDate || "" }}
      </el-descriptions-item>
      <el-descriptions-item label="当前状态" :span="2">
        <dict-tag :options="borrow_status" :value="detailObj.borrowStatus" />
      </el-descriptions-item>
      <el-descriptions-item label="情况说明" :span="3">{{ detailObj.abortExplain || "" }}</el-descriptions-item>
      <el-descriptions-item label="查看资料" :span="3">
        <el-link
          :underline="false"
          type="primary"
          @click="handlePreview(detailObj?.abortFile)"
        >
          {{ detailObj?.abortFile?.fileName ?? "" }}
        </el-link>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <div>
        <el-button @click="cancel">关闭</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='CompleteForm'>
import { borrowDetail, borrowDoneAgree, borrowDoneReject } from "@/api/admin/physical";
import { openFile } from "@/utils/filePreview";

const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const { borrow_status } = proxy.useDict("electronic_cluster_type", "borrow_status");

const dialogVisible = ref(false);
const detailObj = ref({});
const form = ref({});

async function showDialog(row) {
  const { data } = await borrowDetail(row.id);
  detailObj.value = data || {};
  form.value = {
    borrowId: data.id || "",
    warehouseId: data.warehouseId || "",
    shelfId: data.shelfId || "",
  }
  dialogVisible.value = true;
}

function handlePreview(obj) {
  if (obj && obj.fileUrl) {
    openFile(obj.fileUrl);
  } else {
    proxy.$message.error("该文件暂无法查看");
  }
}

async function submitForm(type) {
  let action = type == 1 ? borrowDoneAgree : borrowDoneReject;
  await action(form.value);
  proxy.$message.success("操作成功");
  emits("getList");
  cancel();
}

function cancel() {
  dialogVisible.value = false;
}
defineExpose({
  showDialog
})
</script>
