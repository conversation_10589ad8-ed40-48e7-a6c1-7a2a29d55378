<template>
  <dialogFullScreen
    title="借出设置"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-descriptions :column="3" border>
      <el-descriptions-item label="资料名称" label-class-name="label">{{ detailObj.materialName || "" }}</el-descriptions-item>
      <el-descriptions-item label="书目编号" label-class-name="label">{{ detailObj.materialNo || "" }}</el-descriptions-item>
      <el-descriptions-item label="资料年份" label-class-name="label">{{ detailObj.materialYear || "" }}</el-descriptions-item>
      <el-descriptions-item label="可借阅" label-class-name="label">{{ detailObj.curStock  }}</el-descriptions-item>
      <el-descriptions-item label="馆藏分类" label-class-name="label">
        <dict-tag :options="electronic_cluster_type" :value="detailObj.materialCluster" />
      </el-descriptions-item>
    </el-descriptions>
    <div class="v-title v-m-y-20">选择出库</div>
    <div class="v-m-y-20">出库：将资料取出资料馆，从库存到未入库。</div>

    <el-table :data="tableData">
      <el-table-column label="所属仓库" prop="warehouseName" align="center" />
      <el-table-column label="所在书架" prop="shelfName" align="center" />
      <el-table-column label="资料数量" prop="realStock" align="center" />
      <el-table-column label="从这借出" width="100" align="center">
        <template #default="{ row, $index }">
          <el-checkbox v-model="row.checked" @change="changeCheck($index)"></el-checkbox>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='LendForm'>
import { distributionStock, borrowAgree } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => []
  },
})

const dialogVisible = ref(false);
const detailObj = ref({});
const tableData = ref([]);
const borrowId = ref("");

async function showDialog(row) {
  borrowId.value = row.id;
  detailObj.value = {};
  tableData.value = [];
  const { data } = await distributionStock(row.materialId);
  detailObj.value = data.materialInfo || {};
  tableData.value = data.stocks || [];
  dialogVisible.value = true;
}

function changeCheck(i) {
  tableData.value.map((val, index) => {
    if (index != i) val.checked = false
  })
}

async function submitForm() {
  let params = null;
  tableData.value.map(val => {
    if (val.checked) {
      params = {
        borrowId: borrowId.value,
        warehouseId: val.warehouseId,
        shelfId: val.shelfId,
      }
    }
  })
  if (!params) return proxy.$message.error("请选择借出位置");

  await borrowAgree(params);
  proxy.$message.success("操作成功");
  emits("getList");
  cancel();
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog
})
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
