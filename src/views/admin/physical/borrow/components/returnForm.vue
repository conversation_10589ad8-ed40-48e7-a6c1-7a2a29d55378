<template>
  <dialogFullScreen
    title="归还入库"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
    <el-descriptions :column="3" border>
      <el-descriptions-item label="资料名称" label-class-name="label">{{ detailObj.materialName || "" }}</el-descriptions-item>
      <el-descriptions-item label="书目编号" label-class-name="label">{{ detailObj.materialNo || "" }}</el-descriptions-item>
      <el-descriptions-item label="资料年份" label-class-name="label">{{ detailObj.materialYear || "" }}</el-descriptions-item>
    </el-descriptions>

    <div class="v-title v-m-y-20">当前库存</div>
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      class="search-form"
    >
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input 
          v-model="queryParams.warehouseName" 
          placeholder="请输入仓库名称" 
          clearable
        />
      </el-form-item>
      <el-form-item label="书架名称" prop="shelfName">
        <el-input 
          v-model="queryParams.shelfName" 
          placeholder="请输入书架名称" 
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData">
      <el-table-column label="所属仓库" prop="warehouseName" align="center" />
      <el-table-column label="所在书架" prop="shelfName" align="center" />
      <el-table-column label="数量" prop="materialStock" align="center" />
      <el-table-column label="归还到这" width="100" align="center">
        <template #default="{ row, $index }">
          <el-checkbox v-model="row.checked" @change="changeCheck($index)"></el-checkbox>
        </template>
      </el-table-column>
    </el-table>

    <div style="position: relative; margin-right: 20px; margin-bottom: 25px;">
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />
    </div>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='ReturnForm'>
import { distributionStock, borrowBack, warehousePage } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);

const dialogVisible = ref(false);
const detailObj = ref({});
const tableData = ref([]);
const borrowId = ref("");
const total = ref(0);
const queryParams = ref({
  current: 1,
  size: 10,
  materialId: "",
})

async function showDialog(row = {}) {
  borrowId.value = row.id;
  detailObj.value = row;
  tableData.value = [];
  queryParams.value.materialId = row.materialId;
  getList();
  dialogVisible.value = true;
}

async function getList() {
  const { data } = await warehousePage(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function changeCheck(i) {
  tableData.value.map((val, index) => {
    if (index != i) val.checked = false
  })
}

async function submitForm() {
  let params = null;
  tableData.value.map(val => {
    if (val.checked) {
      params = {
        borrowId: borrowId.value,
        warehouseId: val.warehouseId,
        shelfId: val.shelfId,
      }
    }
  })
  if (!params) return proxy.$message.error("请选择归还位置");

  await borrowBack(params);
  proxy.$message.success("操作成功");
  emits("getList");
  cancel();
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog
})
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
