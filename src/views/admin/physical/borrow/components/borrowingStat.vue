<template>
  <dialogFullScreen
    title="查看异常记录"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
  <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          disabled
        />
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="借阅人" prop="borrowUserName" align="center" />
      <el-table-column label="单位(处室)" prop="borrowUserDeptName" align="center" />
      <el-table-column label="联系方式" prop="borrowUserPhone" align="center" />
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li
              style="color: #67C23A"
              @click="handleDone(row)"
            >
              <el-icon><TopLeft /></el-icon>查看办结处理
            </li>
            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />
  </dialogFullScreen>
  <delCompleted ref="delCompletedRef"  />
</template>
<script setup name='BorrowingStat'>
import delCompleted from './delCompleted'
import { getBorrowaBnormalRecord } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  borrow_status: {
    type: Array,
    default: () => []
  }
})
const dialogVisible = ref(false);
const total = ref(0);
const loading = ref(false);
const queryParams = reactive({
  current: 1,
  size: 10,
  materialName: "",
  materialId: '',
});
const showSearch = ref(true);
const tableData = ref([]);
async function showDialog(row) {
  queryParams.materialId = row.id;
  queryParams.materialName = row.materialName;
  dialogVisible.value = true;
  getList()
}
async function getList() {
  let { materialName, ...params } = queryParams;
  const { data } = await getBorrowaBnormalRecord(params);
  console.log(data)
  tableData.value = data.records || [];
}
function cancel() {
  dialogVisible.value = false;
}
function handleDone(row) {
  proxy.$refs.delCompletedRef.showDialog(row)
}
defineExpose({
  showDialog
})
</script>
