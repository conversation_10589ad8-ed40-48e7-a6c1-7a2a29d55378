<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="仓库编号" prop="warehouseNo">
        <el-input
          v-model="queryParams.warehouseNo"
          placeholder="请输入仓库编号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新建仓库</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="仓库名称" prop="warehouseName" align="center" />
      <el-table-column label="仓库编号" prop="warehouseNo" align="center" />
      <el-table-column label="是否置顶" align="center">
        <template #default="{ row }">
          <dict-tag :options="exist_abnormal_type" :value="row.topFlag" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li style="color: #409eff" @click="handleEdit(row)">
              <el-icon><EditPen /></el-icon>编辑
            </li>
            <li style="color: #f56c6c" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>删除
            </li>

            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <wareForm ref="wareFormRef" @getList="getList" />
  </div>
</template>

<script setup name="RackSetting">
import wareForm from "./components/wareForm.vue";
const { proxy } = getCurrentInstance();
import { warePageList, wareDelete } from "@/api/admin/physical";
const { exist_abnormal_type } = proxy.useDict("exist_abnormal_type");
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    warehouseName: "",
    warehouseNo: "",
  },
  tableData: [],
});

const { queryParams, tableData } = toRefs(data);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  const { data } = await warePageList(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

function handleAdd() {
  proxy.$refs.wareFormRef.showDialog("add");
}

function handleEdit(row) {
  proxy.$refs.wareFormRef.showDialog("edit", row);
}

function handleDelete(row) {
  proxy
    .$confirm("确认删除吗？", "提示")
    .then(async () => {
      await wareDelete(row.id);
      proxy.$message.success("操作成功");
      getList();
    })
    .catch(() => {});
}

getList();
</script>
