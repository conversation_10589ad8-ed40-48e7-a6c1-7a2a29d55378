<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="书架名称" prop="shelfName">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-popover
          placement="top-start"
          title=""
          :width="430"
          trigger="hover"
          content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
        >
          <template #reference>
            <el-button type="info" plain icon="Download" @click="handleExport"
              >导出清单</el-button
            >
          </template>
        </el-popover>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="仓库名称" prop="warehouseName" align="center" />

      <el-table-column label="书架名称" prop="shelfName" align="center" />

      <el-table-column label="上架资料" prop="materialName" align="center" />
      <el-table-column label="印刷时间" prop="printDate" align="center" />
      <el-table-column label="资料数量" prop="realStock" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <el-tag :style="{ 'margin-top': total > 0 ? '-40px' : '20px' }" size="large"
      >资料总数：{{ summaryNum || 0 }}</el-tag
    >
  </div>
</template>

<script setup name="RackManage">
const { proxy } = getCurrentInstance();
import {
  warehouseList,
  // warehouseStockListExport,
  // shelfListExport,
  bookPageListExport,
  warehouseSummary,
} from "@/api/admin/physical";

const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    warehouseName: "",
    shelfName: "",
  },
  tableData: [],
  summaryNum: 0,
});

const { queryParams, tableData, summaryNum } = toRefs(data);

const route = useRoute();
const pageType = computed(() => {
  // 1-仓库管理 2-书架管理
  return route.query.pageType ?? 1;
});

function handleQuery() {
  queryParams.value.current = 1;
  getList();
  getSummary();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  const { data } = await warehouseList(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

/** 导出按钮操作 */
function handleExport() {
  let params = {
    ...queryParams.value,
  };

  bookPageListExport(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `仓库查询${new Date().getTime()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    // proxy.$message.success("导出成功");
  });
}

async function getSummary() {
  const data = await warehouseSummary(queryParams.value);
  summaryNum.value = data?.data || 0;
}

getList();
getSummary();
</script>
