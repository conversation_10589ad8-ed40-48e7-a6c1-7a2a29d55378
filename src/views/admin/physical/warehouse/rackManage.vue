<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="书架名称" prop="shelfName">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入书架名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="书架编号" prop="shelfNo">
        <el-input
          v-model="queryParams.shelfNo"
          placeholder="请输入书架编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属仓库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择所属仓库"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in waresArr"
            :key="index"
            :label="item.warehouseName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >新建书架</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="书架名称" prop="shelfName" align="center" />
      <el-table-column label="书架编号" prop="shelfNo" align="center" />

      <el-table-column label="是否置顶" align="center">
        <template #default="{ row }">
          <dict-tag :options="exist_abnormal_type" :value="row.topFlag" />
        </template>
      </el-table-column>

      <el-table-column label="所属仓库" prop="warehouseName" align="center" />
      <el-table-column label="操作" align="center" width="100">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li style="color: #409eff" @click="handleEdit(row)">
              <el-icon><EditPen /></el-icon>编辑
            </li>
            <li style="color: #f56c6c" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>删除
            </li>

            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <bookForm ref="bookFormRef" :waresArr="waresArr" @getList="getList" />
  </div>
</template>

<script setup name="RackManage">
import bookForm from "./components/bookForm.vue";
const { proxy } = getCurrentInstance();
import { bookPageList, bookDelete, warePageList } from "@/api/admin/physical";

const { exist_abnormal_type } = proxy.useDict("exist_abnormal_type");
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    shelfName: "",
    shelfNo: "",
    warehouseId: "",
  },
  tableData: [],
  waresArr: [],
});

const { queryParams, tableData, waresArr } = toRefs(data);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  const { data } = await bookPageList(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

async function getWares() {
  const { data } = await warePageList({ current: 1, size: 1000 });
  waresArr.value = data.records || [];
}

function handleAdd() {
  proxy.$refs.bookFormRef.showDialog("add");
}

function handleEdit(row) {
  proxy.$refs.bookFormRef.showDialog("edit", row);
}

function handleDelete(row) {
  proxy
    .$confirm("确认删除吗？", "提示")
    .then(async () => {
      await bookDelete(row.id);
      proxy.$message.success("操作成功");
      getList();
    })
    .catch(() => {});
}

getList();
getWares();
</script>
