<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item
        v-if="['1'].includes(pageType)"
        label="资料名称"
        prop="materialName"
      >
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>

      <el-form-item
        v-if="['2'].includes(pageType)"
        label="书架名称"
        prop="shelfName"
      >
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="所属仓库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择所属仓库"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in waresArr"
            :key="index"
            :label="item.warehouseName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-popover
          placement="top-start"
          title=""
          :width="430"
          trigger="hover"
          content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
        >
          <template #reference>
            <el-button type="info" plain icon="Download" @click="handleExport"
              >导出清单</el-button
            >
          </template>
        </el-popover>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column
        v-if="['1'].includes(pageType)"
        label="资料名称"
        prop="materialName"
        align="center"
      />

      <el-table-column
        v-if="['2'].includes(pageType)"
        label="书架名称"
        prop="shelfName"
        align="center"
      />

      <el-table-column label="所属仓库" prop="warehouseName" align="center" />
      <el-table-column
        v-if="['2'].includes(pageType)"
        label="仓架编号"
        prop="warehouseShelfNo"
        align="center"
      />
      <el-table-column
        v-if="['1'].includes(pageType)"
        label="资料数量"
        prop="realStock"
        align="center"
      />

      <el-table-column
        v-if="['2'].includes(pageType)"
        label="资料数量"
        prop="shelfStock"
        align="center"
      />

      <el-table-column label="操作" align="center" width="150">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleDetail(row)"
            >查看</el-button
          >
          <el-button link type="primary" @click="handleMove(row)"
            >资料{{ { "1": "挪仓", "2": "挪架" }[pageType] }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <dialogDetail ref="dialogDetailRef" :pageType="pageType" />
    <moveFile ref="moveFileRef" @getList="getList" :pageType="pageType" />
  </div>
</template>

<script setup name="RackManage">
import moveFile from "./components/moveFile.vue";
import dialogDetail from "./components/dialogDetail.vue";
const { proxy } = getCurrentInstance();
import {
  warePageList,
  warehouseStockList,
  bookPageList,
  warehouseStockListExport,
  shelfListExport,
} from "@/api/admin/physical";

const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    materialName: "",
    warehouseId: "",
  },
  tableData: [],
  waresArr: [],
});

const { queryParams, tableData, waresArr } = toRefs(data);

const route = useRoute();
const pageType = computed(() => {
  // 1-仓库管理 2-书架管理
  return route.query.pageType ?? 1;
});

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  const queryApi = { 1: warehouseStockList, 2: bookPageList }[pageType.value];
  const { data } = await queryApi(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

async function getWares() {
  const { data } = await warePageList({ current: 1, size: 1000 });
  waresArr.value = data.records || [];
}

function handleDetail(row) {
  proxy.$refs.dialogDetailRef.showDialog(row);
}

function handleMove(row) {
  proxy.$refs.moveFileRef.showDialog(row);
}

/** 导出按钮操作 */
function handleExport() {
  let params = {
    ...queryParams.value,
  };

  const exportlApi = { 1: warehouseStockListExport, 2: shelfListExport }[
    pageType.value
  ];
  exportlApi(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `${
        { 1: "仓库管理", 2: "书架管理" }[pageType.value]
      }${new Date().getTime()}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    // proxy.$message.success("导出成功");
  });
}

getList();
getWares();
</script>
