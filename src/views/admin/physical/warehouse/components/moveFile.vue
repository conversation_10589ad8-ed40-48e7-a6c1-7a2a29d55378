<template>
  <dialogFullScreen
    :title="dialogTitle"
    v-model="dialogVisible"
    width="80%"
    append-to-body
  >
    <div class="v-m-20" v-if="dialogVisible">
      <el-descriptions :column="3" border v-if="['1'].includes(pageType)">
        <el-descriptions-item label="资料名称" label-class-name="label">{{
          detailObj.materialName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="书目编号" label-class-name="label">{{
          detailObj.materialNo || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="总出库量">{{
          detailObj.dispatchStock
        }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" border v-if="['2'].includes(pageType)">
        <el-descriptions-item label="当前书架" label-class-name="label">{{
          detailObj.shelfName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="当前仓库" label-class-name="label">{{
          detailObj.warehouseName || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="仓架编号">{{
          detailObj.warehouseShelfNo || ""
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="v-m-y-20">出库说明：库存出库数量，需要小于等于库存总数。</div>

      <el-form ref="formRef" :model="form" label-width="100px">
        <el-row
          v-if="['1'].includes(pageType)"
          :gutter="5"
          v-for="(item, index) in form.domains"
          :key="index"
        >
          <el-col :span="5">
            <el-form-item
              label="所在书架:"
              :prop="'domains.' + index + '.shelfId'"
            >
              {{ item.shelfName || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item
              label="资料数量:"
              :prop="'domains.' + index + '.realStock'"
            >
              {{ item.shelfStock || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item
              label="挪出量:"
              :prop="'domains.' + index + '.stockNum'"
              :rules="{
                required: false,
                message: '请输入挪出量',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.stockNum"
                placeholder="请输入挪出量"
                @input="
                  (value) =>
                    (item.stockNum = limitNumber(value, item.realStock, 0))
                "
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item
              label="目标仓库"
              :prop="'domains.' + index + '.targetWarehouseId'"
              :rules="
                item.stockNum
                  ? {
                      required: true,
                      message: '请选择目标仓库',
                      trigger: 'change',
                    }
                  : null
              "
            >
              <el-select
                v-model="item.targetWarehouseId"
                placeholder="请选择目标仓库"
                filterable
                @change="(value) => getBooks(value, item)"
              >
                <el-option
                  v-for="(item, index) in waresArr"
                  :key="index"
                  :label="item.warehouseName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item
              label="目标书架"
              :prop="'domains.' + index + '.targetShelfId'"
              :rules="
                item.stockNum
                  ? {
                      required: true,
                      message: '请选择目标书架',
                      trigger: 'change',
                    }
                  : nulll
              "
            >
              <el-select
                v-model="item.targetShelfId"
                placeholder="请选择目标书架"
                filterable
              >
                <el-option
                  v-for="(item, index) in item.booksArr || []"
                  :key="index"
                  :label="item.shelfName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          v-if="['2'].includes(pageType)"
          :gutter="20"
          v-for="(item, index) in form.domains"
          :key="index"
        >
          <el-col :span="6">
            <el-form-item
              label="资料名称:"
              :prop="'domains.' + index + '.shelfId'"
            >
              {{ item.materialName || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="资料编号:"
              :prop="'domains.' + index + '.materialNo'"
            >
              {{ item.materialNo || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="资料数量:"
              :prop="'domains.' + index + '.realStock'"
            >
              {{ item.realStock || "" }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="挪出量:"
              :prop="'domains.' + index + '.stockNum'"
              :rules="{
                required: false,
                message: '请输入挪出量',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.stockNum"
                placeholder="请输入挪出量"
                @input="
                  (value) =>
                    (item.stockNum = limitNumber(value, item.realStock, 0))
                "
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider />
        <el-row
          :gutter="20"
          v-if="['2'].includes(pageType) && form.domains.length"
        >
          <el-col :span="8">
            <el-form-item
              label="目标仓库"
              prop="targetWarehouseId"
              :rules="{
                required: true,
                message: '请选择目标仓库',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="form.targetWarehouseId"
                placeholder="请选择目标仓库"
                filterable
                @change="(value) => getBooks(value)"
              >
                <el-option
                  v-for="(item, index) in waresArr"
                  :key="index"
                  :label="item.warehouseName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="目标书架"
              prop="targetShelfId"
              :rules="{
                required: true,
                message: '请选择目标书架',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="form.targetShelfId"
                placeholder="请选择目标书架"
                filterable
              >
                <el-option
                  v-for="(item, index) in booksArr || []"
                  :key="index"
                  :label="item.shelfName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button type="primary" @click="submitForm">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="OutForm">
import {
  warehouseStockDetail,
  physicalStockPage,
  warePageList,
  bookPageList,
  movewarehouseApi,
  moveShelfApi,
} from "@/api/admin/physical";

const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
const props = defineProps({
  electronic_cluster_type: {
    type: Array,
    default: () => [],
  },
  dialogTitle: {
    type: String,
    default: "资料挪架",
  },
  pageType: {
    type: String,
    default: "",
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});
const form = reactive({
  domains: [],
});
const waresArr = ref([]);
const booksArr = ref([]);

async function showDialog(row) {
  let params = {
    warehouseId: row.warehouseId,
  };

  if (props.pageType == "1") {
    params.materialId = row.id;
  }

  if (props.pageType == "2") {
    params.shelfId = row.id;
  }

  detailObj.value = row;

  const queryApi = { 1: warehouseStockDetail, 2: physicalStockPage }[
    props.pageType
  ];
  const { data } = await queryApi(params);

  form.domains = data.records;
  form?.domains?.forEach((element) => {
    element.warehouseId = row.warehouseId;
    element.shelfId = element.id;
    element.materialId = element.id;
    element.shelfStock = element.realStock || element.shelfStock;
  });

  if (props.pageType == "2") {
    form.targetWarehouseId = "";
    form.targetShelfId = "";
  }
  dialogVisible.value = true;
  getWares();
}

function limitNumber(value, max, decimal) {
  value = proxy.floatNumber(value, decimal, true);
  if (value > max) value = max;

  return value;
}

async function getWares() {
  const { data } = await warePageList({ current: 1, size: 1000 });
  waresArr.value = data.records || [];
}

async function getBooks(value, item = {}, shelfId = "") {
  const { data } = await bookPageList({
    current: 1,
    size: 1000,
    warehouseId: value,
  });
  booksArr.value = data.records;
  // item["targetShelfId"] = shelfId;
  item["booksArr"] = data.records || [];
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;

    let params = {
      warehouseId: detailObj.value.warehouseId,
    };
    if (props.pageType == "1") {
      params.materialId = detailObj.value.id;
      params.moveWarehouseList = form.domains;
    }

    if (props.pageType == "2") {
      params.shelfId = detailObj.value.id;
      params.moveShelfList = form.domains;
      params.targetWarehouseId = form.targetWarehouseId;
      params.targetShelfId = form.targetShelfId;
    }

    const queryApi = { 1: movewarehouseApi, 2: moveShelfApi }[props.pageType];
    const { data } = await queryApi(params);

    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
