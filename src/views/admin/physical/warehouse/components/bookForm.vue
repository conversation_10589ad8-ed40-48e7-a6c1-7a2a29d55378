<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="50%"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      style="margin-top: 20px"
    >
      <el-form-item label="书架名称" prop="shelfName">
        <el-input v-model="form.shelfName" placeholder="请输入书架名称" />
      </el-form-item>
      <el-form-item label="书架编号" prop="shelfNo">
        <el-input v-model="form.shelfNo" placeholder="请输入书架编号" />
      </el-form-item>
      <el-form-item label="所属仓库" prop="warehouseId">
        <el-select
          v-model="form.warehouseId"
          placeholder="请选择所属仓库"
          filterable
        >
          <el-option
            v-for="(item, index) in props.waresArr"
            :key="index"
            :label="item.warehouseName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="仓库置顶" prop="topFlag">
        <el-switch v-model="form.topFlag" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="BookForm">
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
import { warePageList, bookSave } from "@/api/admin/physical";

const props = defineProps({
  waresArr: {
    type: Array,
    default: () => [],
  },
});

const title = ref("");
const dialogVisible = ref(false);
const data = reactive({
  form: {
    topFlag: false,
  },
  rules: {
    shelfName: [{ required: true, message: "请输入书架名称", trigger: "blur" }],
    shelfNo: [{ required: true, message: "请输入书架编号", trigger: "blur" }],
    warehouseId: [
      { required: true, message: "请选择所属仓库", trigger: "change" },
    ],
    topFlag: [{ required: true, message: "请选择仓库置顶", trigger: "blur" }],
  },
});

const { form, rules } = toRefs(data);

function showDialog(type, row = {}) {
  form.value = {
    shelfName: "",
    shelfNo: "",
    warehouseId: "",
  };
  proxy.resetForm("formRef");
  title.value = type == "add" ? "新建书架" : "编辑书架";
  if (row) {
    form.value = { ...form.value, ...row };
    form.value.topFlag = !!row.topFlag;
  }

  dialogVisible.value = true;
}

function submitForm() {
  proxy.$refs.formRef.validate(async (valid) => {
    if (!valid) return;
    const params = {
      ...form.value,
      topFlag: form.value.topFlag ? 1 : 0,
    };
    await bookSave(params);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  });
}

function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>
