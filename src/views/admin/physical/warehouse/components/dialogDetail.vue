<template>
  <dialogFullScreen
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    append-to-body
  >
    <div class="v-m-20">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="当前仓库" label-class-name="label">{{
          detailObj.warehouseName || ""
        }}</el-descriptions-item>

        <el-descriptions-item
          v-if="['2'].includes(pageType)"
          label="当前书架"
          label-class-name="label"
          >{{ detailObj.shelfName || "" }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="['2'].includes(pageType)"
          label="仓架编号"
          label-class-name="label"
          >{{ detailObj.warehouseShelfNo || "" }}</el-descriptions-item
        >

        <el-descriptions-item
          v-if="['1'].includes(pageType)"
          label="当前资料"
          label-class-name="label"
          >{{ detailObj.materialName || "" }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="['1'].includes(pageType)"
          label="资料编号"
          label-class-name="label"
          >{{ detailObj.materialNo || "" }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="['1'].includes(pageType)"
          label="资料数量"
          label-class-name="label"
          >{{ detailObj.realStock || "" }}</el-descriptions-item
        >
      </el-descriptions>
      <div class="v-m-y-20" v-if="['1'].includes(pageType)">
        库存说明：没有书架编号的资料，即为未上架。
      </div>
      <el-table class="v-m-y-20" border :data="detailObj.recordList || []">
        <el-table-column
          v-if="['2'].includes(pageType)"
          label="资料名称"
          prop="materialName"
          align="center"
        />
        <el-table-column
          v-if="['2'].includes(pageType)"
          label="书目编号"
          prop="materialNo"
          align="center"
        />
        <el-table-column
          v-if="['2'].includes(pageType)"
          label="印刷时间"
          prop="printDate"
          align="center"
        />
        <el-table-column
          v-if="['1'].includes(pageType)"
          label="书架名称"
          prop="shelfName"
          align="center"
        />
        <el-table-column
          v-if="['1'].includes(pageType)"
          label="书架编号"
          prop="shelfNo"
          align="center"
        />

        <el-table-column
          v-if="['1'].includes(pageType)"
          label="资料数量"
          prop="shelfStock"
          align="center"
        />
        <el-table-column
          v-if="['2'].includes(pageType)"
          label="资料数量"
          prop="realStock"
          align="center"
        />
      </el-table>
    </div>
    <template #footer>
      <div class="v-m-r-20">
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name="InOutDetail">
import { warehouseStockDetail, physicalStockPage } from "@/api/admin/physical";
const { proxy } = getCurrentInstance();
const props = defineProps({
  physical_inout_types: {
    type: Array,
    default: () => [],
  },
  pageType: {
    type: String,
    default: "",
  },
  dialogTitle: {
    type: String,
    default: "查看",
  },
});

const dialogVisible = ref(false);
const detailObj = ref({});

async function showDialog(row) {
  let params = {
    warehouseId: row.warehouseId,
  };

  if (props.pageType == "1") {
    params.materialId = row.id;
  }

  if (props.pageType == "2") {
    params.shelfId = row.id;
  }

  detailObj.value = row;

  const detailApi = { 1: warehouseStockDetail, 2: physicalStockPage }[
    props.pageType
  ];
  const { data } = await detailApi(params);
  detailObj.value.recordList = data.records || {};
  dialogVisible.value = true;
}

function cancel() {
  dialogVisible.value = false;
}

defineExpose({
  showDialog,
});
</script>

<style lang="scss" scoped>
:deep(.label) {
  width: 120px;
}
</style>
