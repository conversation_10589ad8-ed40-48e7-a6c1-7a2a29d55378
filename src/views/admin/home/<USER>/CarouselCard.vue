<template>
  <div class="carousel-warp">
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>
<script setup name="CarouselCard">
  const props = defineProps({
    title: {
      type: String,
      default: "",
    },
  });
</script>
<style lang="scss" scoped>
.carousel-warp{
  padding: 20px;
  .card-title{
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .card-content{
    padding: 0 10px;
  }
}
</style>
