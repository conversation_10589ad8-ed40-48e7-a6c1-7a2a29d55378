<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="出入库" prop="materialName">
        <el-select
          v-model="queryParams.recordType"
          placeholder="请选择出入库类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in [
              { label: '入库', value: '1' },
              { label: '出库', value: '2' },
            ]"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="书目编号" prop="materialNo">
        <el-input
          v-model="queryParams.materialNo"
          placeholder="请输入书目编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="所在仓库" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入所在仓库"
          clearable
        />
      </el-form-item>
      <el-form-item label="所在书架" prop="shelfName">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入所在书架"
          clearable
        />
      </el-form-item>
      <el-form-item label="操作时间" prop="stockStartDate">
        <el-date-picker
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="queryParams.stockStartDate"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-popover
          placement="top-start"
          title=""
          :width="430"
          trigger="hover"
          content="按照筛选结果生产EXCEL ，初始情况下为全部资料，下载到本地"
        >
          <template #reference>
            <el-button type="info" plain icon="Download" @click="handleExport"
              >导出资料清单</el-button
            >
          </template>
        </el-popover>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="资料名称" prop="materialName" align="center" />
      <el-table-column label="书目编号" prop="materialNo" align="center" />
      <el-table-column label="所在仓库" prop="warehouseName" align="center" />
      <el-table-column label="所在书架" prop="shelfName" align="center" />
      <el-table-column label="操作时间" prop="createTime" align="center" />
      <el-table-column label="出入库" align="center">
        <template #default="{ row }">
          <dict-tag :options="physical_inout_types" :value="row.recordType" />
        </template>
      </el-table-column>
      <el-table-column label="出库对象" prop="outTarget" align="center" />
      <el-table-column label="数量" prop="stockNum" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="RackManage">
/* import { bookPageList, warePageList } from "@/api/admin/physical"; */
import { getRecordList, exportStockInOut } from "@/api/admin/godown";
const { proxy } = getCurrentInstance();
const { physical_inout_types } = proxy.useDict("physical_inout_types");
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    shelfName: "",
    shelfNo: "",
    warehouseName: "",
    recordTypeList: [1, 2], //出入库类型 1-入库 2-出库  3-派送出库
    stockStartDate: "",
    stockEndDate: "",
    recordType: "",
  },
  tableData: [],
  wareList: [],
  bookList: [],
});
const { queryParams, tableData, wareList, bookList } = toRefs(data);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  queryParams.value.recordType = "";
  proxy.resetForm("queryRef");
  handleQuery();
}
async function getList() {
  let queryForm = {
    ...queryParams.value,
    stockStartDate: queryParams.value.stockStartDate
      ? queryParams.value.stockStartDate[0]
      : "",
    stockEndDate: queryParams.value.stockStartDate
      ? queryParams.value.stockStartDate[1]
      : "",
  };
  const { data } = await getRecordList(queryForm);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}

/** 导出按钮操作 */
function handleExport() {
  let params = {
    ...queryParams.value,
  };
  exportStockInOut(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `库存出入库记录${new Date().getTime()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    link.remove();
    // proxy.$message.success("导出成功");
  });
}
getList();
</script>
