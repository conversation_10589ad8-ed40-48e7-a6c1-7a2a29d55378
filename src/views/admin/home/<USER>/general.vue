<template>
  <div class="app-container">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="书架名称" prop="shelfName">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入书架名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="所在仓库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择所在仓库"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in waresArr"
            :key="index"
            :label="item.warehouseName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="书架名称" prop="shelfName" align="center" />
      <el-table-column label="所在仓库" prop="warehouseName" align="center" />
      <el-table-column label="资料数量" prop="shelfStock" align="center" />
      <el-table-column label="操作" align="center" width="100">
        <template #default="{ row }">
          <el-popover placement="left" popper-class="operate-ul">
            <li
              style="color: #409EFF"
              @click="handleDialog(row, 'stash')"
            >
            <el-icon><Collection /></el-icon>当前存放
            </li>
            <li
              style="color: #f56c6c"
              @click="handleDialog(row, 'record')"
            >
            <el-icon><Document /></el-icon>存放记录
            </li>
            <template #reference>
              <el-icon size="20" class="v-pointer"><MoreFilled /></el-icon>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <recordForm ref="recordFormRef" :waresArr="waresArr" />
    <stashTable ref="stashTableRef" />
  </div>
</template>

<script setup name='RackManage'>
import recordForm from "./components/recordForm.vue";
import stashTable from "./components/stashTable.vue";
const { proxy } = getCurrentInstance();
import { getOverviewList } from "@/api/admin/godown";
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    shelfName: "",
    warehouseId: "",
  },
  tableData: [],
  waresArr: [],
})

const { queryParams, tableData, waresArr } = toRefs(data);

function handleQuery() {
  queryParams.value.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

async function getList() {
  const { data } = await getOverviewList(queryParams.value);
  tableData.value = data.records || [];
  total.value = data.total || 0;
}
function handleDialog(row, type) {
  if (type === "record") {
    proxy.$refs.recordFormRef.showDialog(row);
  }else {
    proxy.$refs.stashTableRef.showDialog(row);
  }
}
getList();
</script>
