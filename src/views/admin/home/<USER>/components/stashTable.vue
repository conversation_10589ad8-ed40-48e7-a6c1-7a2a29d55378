<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
  <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="所在仓库" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入书架名称"
          clearable
          disabled
        />
      </el-form-item>
      <el-form-item label="当前书架" prop="shelfName">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入书架名称"
          clearable
          disabled
        />
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="书架名称" prop="shelfName" align="center" />
      <el-table-column label="所在仓库" prop="warehouseName" align="center" />
      <el-table-column label="资料数量" prop="realStock" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <template #footer>
      <div>
        <el-button @click="cancel">关闭</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='BookForm'>
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
import { getStockPageList } from "@/api/admin/godown";
const props = defineProps({
  waresArr: {
    type: Array,
    default: () => []
  }
})
const queryParams = ref({
  current: 1,
  size: 10,
  shelfName: "",
  shelfId: "",
  warehouseName: "",
  warehouseId: "",
});
const total = ref(0);
const showSearch = ref(true);
const loading = ref(false);
const tableData = ref([]);
const title = ref("");
const dialogVisible = ref(false);
function showDialog(row = {}) {
  title.value = "当前存放"
  dialogVisible.value = true;
  console.log(queryParams.value)
  console.log(row)
  let { warehouseName, warehouseId, shelfName, shelfId } = row;
  queryParams.value = {
    shelfName: "",
    warehouseName: "",
  }
  if (row) {
    queryParams.value = {
      shelfName: shelfName,
      warehouseName: warehouseName,
    }
  }
  let queryForm = {
    current: 1,
    size: 10,
    shelfId,
    warehouseId,
  }
  tableData.value = [];
  loading.value = true;
  getStockPageList(queryForm).then((res) => {
    tableData.value = res.data.records;
    total.value = res.total;
    loading.value = false;
  });
}
function getList() {
}
function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}
defineExpose({
  showDialog
})
</script>
