<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="70%"
    append-to-body
  >
  <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      v-show="showSearch"
      class="search-form"
    >
      <el-form-item label="资料名称" prop="warehouseId">
        <el-input
          v-model="queryParams.shelfName"
          placeholder="请输入资料名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态" prop="shelfName">
        <el-select v-model="queryParams.recordType" placeholder="请选择状态">
            <el-option v-for="item in physical_inout_types" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="资料名称" prop="materialName" align="center" />
      <el-table-column label="状态" prop="recordType" align="center">
        <template #default="{ row }">
          <dict-tag :options="physical_inout_types" :value="row.recordType" />
        </template>
      </el-table-column>
      <el-table-column label="时间" prop="createTime" align="center" />
      <el-table-column label="数量" prop="stockNum" align="center" />
    </el-table>
    <div style="padding: 0 20px 30px 0; position: relative;">
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />
    </div>

    <template #footer>
      <div>
        <el-button @click="cancel">关闭</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='BookForm'>
import { getRecordList } from "@/api/admin/godown";
const { proxy } = getCurrentInstance();
const { physical_inout_types } = proxy.useDict("physical_inout_types");
const emits = defineEmits(["getList"]);
const props = defineProps({
  waresArr: {
    type: Array,
    default: () => []
  }
})
const queryParams = reactive({
  current: 1,
  size: 10,
  recordType: "",
  materialName: "",
  warehouseId: ''
});
const total = ref(0);
const showSearch = ref(true);
const loading = ref(false);
const tableData = ref([]);
const title = ref("");
const dialogVisible = ref(false);
function showDialog(row = {}) {
  console.log("row", row);
  proxy.resetForm("formRef");
  title.value = "存放记录";
  dialogVisible.value = true;
  let { warehouseId } = row;
  queryParams.warehouseId = warehouseId;
  getList();
}
function getList() {
  let queryForm = { ...queryParams}
  getRecordList(queryForm).then((res) => {
    tableData.value = res.data.records;
    total.value = res.data.total;
  });
}
function handleQuery() {
  queryParams.current = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}

defineExpose({
  showDialog
})
</script>
