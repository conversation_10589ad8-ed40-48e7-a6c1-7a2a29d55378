<template>
  <dialogFullScreen
    :title="title"
    v-model="dialogVisible"
    width="50%"
    append-to-body
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" style="margin-top: 20px;">
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
      </el-form-item>
      <el-form-item label="仓库编号" prop="warehouseNo">
        <el-input v-model="form.warehouseNo" placeholder="请输入仓库编号" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </dialogFullScreen>
</template>

<script setup name='WareForm'>
const { proxy } = getCurrentInstance();
const emits = defineEmits(["getList"]);
import { wareSave } from "@/api/admin/physical";

const title = ref("");
const dialogVisible = ref(false);
const data = reactive({
  form: {},
  rules: {
    warehouseName: [
      { required: true, message: "请输入仓库名称", trigger: "blur" }
    ],
    warehouseNo: [
      { required: true, message: "请输入仓库编号", trigger: "blur" }
    ],
  },
});

const { form, rules, } = toRefs(data);

function showDialog(type, row = {}) {
  form.value = {
    warehouseName: "",
    warehouseNo: "",
  };
  proxy.resetForm("formRef");
  title.value = type == "add" ? "新建仓库" : "编辑仓库";
  if (row) form.value = {...form.value, ...row};
  dialogVisible.value = true;
}

function submitForm() {
  proxy.$refs.formRef.validate(async valid => {
    if (!valid) return
    await wareSave(form.value);
    proxy.$message.success("操作成功");
    emits("getList");
    cancel();
  })
}

function cancel() {
  proxy.resetForm("formRef");
  dialogVisible.value = false;
}

defineExpose({
  showDialog
})
</script>
