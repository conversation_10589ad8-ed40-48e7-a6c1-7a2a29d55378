<template>
  <div class="home-container">
    <CarouselCard title="轮播图设置（轮播最多五张）：">
      <CarouselUpload
        :data="carouselList"
        @value-changed="handleChange"
      ></CarouselUpload>
    </CarouselCard>
    <div class="home-btn">
      <el-button type="primary" @click="addCarousel">保存</el-button>
    </div>
  </div>
</template>
<script setup name="Home">
import { addHomeCarousel, getHomeCarousel } from "@/api/admin/home.js";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";
import CarouselUpload from "./components/CarouselUpload.vue";
import CarouselCard from "./components/CarouselCard.vue";

const { proxy } = getCurrentInstance();

const carouselList = ref([]);
const carouselListNew = ref([]);
function addCarousel() {
  proxy.$modal.loading();
  let params = deepClone(carouselListNew.value);
  params.forEach((item) => {
    if (item.comFileList && item.comFileList.length > 0)
      item.comFile = item.comFileList[0];
  });
  addHomeCarousel({ sysRollList: params, subjectType: "electronic_home" })
    .then((res) => {
      getCarousel();
      ElMessage({
        message: "保存成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function getCarousel() {
  proxy.$modal.loading();
  getHomeCarousel({ subjectType: "electronic_home" })
    .then((res) => {
      carouselList.value = res.data;
      console.log(carouselList.value);
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function handleChange(itemList) {
  carouselListNew.value = itemList;
}
getCarousel();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .input-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .el-form-item {
      width: 49%;
    }
  }
  .home-btn {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
