<template>
  <div class="app-container">
    <div class="v-title v-m-y-20">图片介绍设置</div>

    <div class="form-title">电子资料馆：</div>
    <el-form :model="electronForm" ref="electronFormRef" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item
            label="图片"
            prop="showPic"
            :rules="{
              required: true,
              message: '请上传图片',
              trigger: 'change',
            }"
          >
            <imageUpload
              v-model="electronForm.showPic"
              :limit="1"
              :fileSize="20"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            label="介绍内容"
            prop="showText"
            :rules="{
              required: true,
              message: '请输入介绍内容',
              trigger: 'blur',
            }"
          >
            <el-input
              v-model="electronForm.showText"
              placeholder="请输入介绍内容"
              type="textarea"
              :rows="5"
              maxlength="200"
              show-word-limit
            />
            <div class="form-btns">
              <el-button @click="clearForm('electronFormRef')">清空</el-button>
              <el-button type="primary" @click="electronSubmit">确认</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-title v-m-t-20">实体资料馆：</div>
    <el-form :model="entityForm" ref="entityFormRef" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item
            label="图片"
            prop="showPic"
            :rules="{
              required: true,
              message: '请上传图片',
              trigger: 'change',
            }"
          >
            <imageUpload
              v-model="entityForm.showPic"
              :limit="1"
              :fileSize="20"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            label="介绍内容"
            prop="showText"
            :rules="{
              required: true,
              message: '请输入介绍内容',
              trigger: 'blur',
            }"
          >
            <el-input
              v-model="entityForm.showText"
              placeholder="请输入介绍内容"
              type="textarea"
              :rows="5"
              maxlength="200"
              show-word-limit
            />
            <div class="form-btns">
              <el-button @click="clearForm('entityFormRef')">清空</el-button>
              <el-button type="primary" @click="entitySubmit">确认</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup name="FrontSetting">
import { onMounted, ref } from "vue";
import {
  homeElectronicSave,
  homePhysicalSave,
  homeElectronicSelect,
  homePhysicalSelect,
} from "@/api/admin/frontSetting";
const { proxy } = getCurrentInstance();

const electronForm = ref({
  showPic: [],
  showText: "",
});
const entityForm = ref({
  showPic: [],
  showText: "",
});

function clearForm(refName) {
  proxy.resetForm(refName);
}

function electronSubmit() {
  proxy.$refs.electronFormRef.validate(async (valid) => {
    if (!valid) return;

    await homeElectronicSave(electronForm.value);
    proxy.$modal.msgSuccess("操作成功");
    elecSearch();
  });
}

async function elecSearch() {
  const { data } = await homeElectronicSelect();

  electronForm.value = data;
}

function entitySubmit() {
  proxy.$refs.entityFormRef.validate(async (valid) => {
    if (!valid) return;

    await homePhysicalSave(entityForm.value);
    proxy.$modal.msgSuccess("操作成功");
    entitySearch();
  });
}

async function entitySearch() {
  const { data } = await homePhysicalSelect();

  entityForm.value = data;
}

onMounted(() => {
  elecSearch();
  entitySearch();
});
</script>

<style lang="scss" scoped>
.form-title {
  color: #333;
  font-size: 17px;
  font-weight: 700;
  margin-bottom: 20px;
  margin-left: 45px;
}
.form-btns {
  width: 100%;
  margin-top: 20px;
  text-align: right;
}
</style>
