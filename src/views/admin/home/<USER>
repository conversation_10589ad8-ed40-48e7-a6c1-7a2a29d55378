<template>
  <div class="app-container home admin-home">
    <div class="admin-home-title">河南省统计资料馆概况</div>
    <el-row :gutter="20" class="home-row-one">
      <el-col :span="12">
        <div class="home-border-line">
          <h2 @click="handlePath('electronics/categoryManage')">电子馆概览</h2>
          <el-row :gutter="10" class="v-m-y-30">
            <el-col :span="10">分类：{{ collectData.cateNum }}类</el-col>
            <el-col :span="10"
              >资料总数：{{ collectData.materialNum }}本</el-col
            >

            <el-col
              :span="4"
              style="text-align: right; position: relative; top: -5px"
            >
              <el-button
                @click="handlePath('electronics/statistics?pageType=2')"
                type="text"
                >收藏总计</el-button
              >
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="10">收藏总量：{{ collectData.subNum }}次</el-col>
            <el-col :span="10">预览总次数：{{ collectData.clickNum }}次</el-col>
          </el-row>

          <!-- <div class="collect-stat">
            <h4>收藏统计</h4>
            <div class="collect-stat-chart">
              <v-bar-hz
                idBox="collectBox"
                :data="collectData"
                width="100%"
                yUnit=""
              />
            </div>
          </div> -->
        </div>
      </el-col>
      <!-- <el-col :span="6">
        <div class="home-border-line">
          <div class="descriptions-title">影音馆概览</div>
          <div class="descriptions-num">
            <span>资料数</span>
            <span>{{ videoLibView.materialNum }}个</span>
          </div>
          <div class="home-desc">
            <h5>高频点击</h5>
            <ul>
              <li
                v-for="(item, index) in videoLibView.clickStats"
                :key="index"
                :class="`stats-line${index}`"
              >
                <template v-if="Number(item.statsCount) > 0">
                  <div class="v-line-1" :class="`stats-line${index}`">
                    <el-tooltip
                      effect="dark"
                      :content="item.materialName"
                      placement="top-start"
                    >
                      {{ item.materialName }}
                    </el-tooltip>
                  </div>
                  <span>{{ item.statsCount }}个</span>
                </template>
              </li>
            </ul>
          </div>
          <div class="home-desc">
            <h5>收藏统计</h5>
            <ul>
              <li
                v-for="(item, index) in videoLibView.subStats"
                :key="index"
                :class="`stats-line${index}`"
              >
                <template v-if="Number(item.statsCount) > 0">
                  <div class="v-line-1" :class="`stats-line${index}`">
                    <el-tooltip
                      effect="dark"
                      :content="item.materialName"
                      placement="top-start"
                    >
                      {{ item.materialName }}
                    </el-tooltip>
                  </div>
                  <span>{{ item.statsCount }}个</span>
                </template>
              </li>
            </ul>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="home-border-line">
          <div class="descriptions-title">图文馆概览</div>
          <div class="descriptions-num">
            <span>资料数</span>
            <span>{{ picLibView.materialNum }}个</span>
          </div>
          <div class="home-desc">
            <h5>高频点击</h5>
            <ul>
              <li
                v-for="(item, index) in picLibView.clickStats"
                :key="index"
                :class="`stats-line${index}`"
              >
                <template v-if="Number(item.statsCount) > 0">
                  <div class="v-line-1" :class="`stats-line${index}`">
                    <el-tooltip
                      effect="dark"
                      :content="item.materialName"
                      placement="top-start"
                    >
                      {{ item.materialName }}
                    </el-tooltip>
                  </div>
                  <span>{{ item.statsCount }}个</span>
                </template>
              </li>
            </ul>
          </div>
          <div class="home-desc">
            <h5>收藏统计</h5>
            <ul>
              <li
                v-for="(item, index) in picLibView.subStats"
                :key="index"
                :class="`stats-line${index}`"
              >
                <template v-if="Number(item.statsCount) > 0">
                  <div class="v-line-1" :class="`stats-line${index}`">
                    <el-tooltip
                      effect="dark"
                      :content="item.materialName"
                      placement="top-start"
                    >
                      {{ item.materialName }}
                    </el-tooltip>
                  </div>
                  <span>{{ item.statsCount }}个</span>
                </template>
              </li>
            </ul>
          </div>
        </div>
      </el-col> -->

      <el-col :span="12">
        <div class="home-border-line">
          <h2
            class="v-m-b-10"
            @click="handlePath('electronics/categoryManage')"
          >
            仓库统计
          </h2>
          <el-row :gutter="10" class="v-m-b-10">
            <el-col :span="12"
              >仓库总数：{{ warehouseView.warehouseCount }}个</el-col
            >
            <el-col :span="12"
              >库存合计：{{ warehouseView.realStockTotal }}本</el-col
            >
          </el-row>

          <el-table :max-height="150" border :data="warehouseView.records">
            <el-table-column
              label="仓库名称"
              align="center"
              prop="warehouseName"
            />
            <el-table-column
              label="书架数量"
              prop="shelfCount"
              align="center"
            />
            <el-table-column label="库存数量" prop="realStock" align="center" />
            <el-table-column
              label="累计入库数量"
              prop="inStockTotal"
              align="center"
            />
            <el-table-column
              label="累计出库数量"
              prop="outStockTotal"
              align="center"
            />
          </el-table>

          <!-- <div class="collect-stat">
            <h4>收藏统计</h4>
            <div class="collect-stat-chart">
              <v-bar-hz
                idBox="collectBox"
                :data="collectData"
                width="100%"
                yUnit=""
              />
            </div>
          </div> -->
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <div class="home-border-line">
          <h2 @click="handlePath('physical/material/database')">实体馆概览</h2>
          <el-row :gutter="10">
            <el-col :span="6" v-for="item in overviewList" :key="item.label">
              <div
                class="overview-item"
                :class="{ 'overview-current': item.path }"
                @click="handlePath(item.path)"
              >
                {{ item.label }}：<span>{{ item.value }}{{ item.unit }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="overview-table">
            <div class="overview-table-top">
              <span>库存概览</span>
              <el-link
                type="primary"
                @click="handlePath('physical/godown/general')"
                >更多<el-icon> <ArrowRight /> </el-icon
              ></el-link>
            </div>
            <el-table border :data="tableData.data1">
              <el-table-column
                label="书架名称"
                prop="shelfName"
                align="center"
              />
              <el-table-column
                label="所在仓库"
                prop="warehouseName"
                align="center"
              />
              <el-table-column
                label="资料数量"
                prop="shelfStock"
                align="center"
              />
            </el-table>
          </div>
          <div class="overview-table">
            <div class="overview-table-top">
              <span>近期派送出库</span>
              <el-link
                type="primary"
                @click="handlePath('physical/material/outRecords?pageType=3')"
                >更多<el-icon> <ArrowRight /> </el-icon
              ></el-link>
            </div>
            <el-table border :data="tableData.data2">
              <el-table-column
                label="资料名称"
                align="center"
                prop="materialName"
              />
              <el-table-column
                label="出库时间"
                prop="createTime"
                align="center"
              />
              <el-table-column
                label="出库对象"
                prop="outTarget"
                align="center"
              />
              <el-table-column
                label="出库数量"
                prop="stockNum"
                align="center"
              />
            </el-table>
          </div>
          <div class="overview-table">
            <div class="overview-table-top">
              <span>近期出库记录</span>
              <el-link
                type="primary"
                @click="handlePath('physical/material/outRecords?pageType=2')"
                >更多<el-icon> <ArrowRight /> </el-icon
              ></el-link>
            </div>
            <el-table border :data="tableData.data3">
              <el-table-column
                label="资料名称"
                prop="materialName"
                align="center"
              />
              <el-table-column
                label="书目编号"
                prop="materialNo"
                align="center"
              />
              <el-table-column
                label="出库对象"
                prop="outTarget"
                align="center"
              />
              <el-table-column
                label="出库时间"
                prop="recordDate"
                align="center"
              />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="home-statistics">
      <div class="home-statistics-top">
        <span>借阅统计</span>
        <el-link
          type="primary"
          @click="handlePath('physical/borrow/borrowStat')"
          >更多<el-icon> <ArrowRight /> </el-icon
        ></el-link>
      </div>
      <el-row :gutter="10" class="home-statistics-chart">
        <el-col :span="8">
          <div class="statistics-chart-line">
            <span>借阅累积统计</span>
            <div>
              <v-bar
                idBox="accrueBox"
                :data="accrueData"
                width="100%"
                yUnit=""
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="statistics-chart-line">
            <span>借阅状态统计</span>
            <div>
              <v-pie
                idBox="statusBox"
                :data="statusData"
                height="320px"
                width="90%"
                yUnit="个"
                :radius="['50%', '70%']"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="statistics-chart-line">
            <span>借阅机构统计</span>
            <div>
              <v-bar
                idBox="throughoutBox"
                :data="throughoutData"
                width="100%"
                yUnit=""
              />
            </div>
          </div>
        </el-col>
        <!-- <el-col :span="12">
          <div class="statistics-chart-line">
            <span>资料统计</span>
            <div>
              <v-bar idBox="inforBox" :data="inforData" width="100%" yUnit="" />
            </div>
          </div>
        </el-col> -->
      </el-row>
    </div>
  </div>
</template>
<script setup name="HomeIndex">
import VBar from "@/components/Charts/v-bar";
import VBarHz from "@/components/Charts/v-bar-hz";
import VPie from "@/components/Charts/v-pie";
import VLine from "@/components/Charts/v-line";
import { ref, onMounted, reactive } from "vue";
import {
  getElecLibView,
  getSubsStatusStats,
  getDeptSubsStats,
  getMaterialStats,
  // getPicLibView,
  // getVideoLibView,
  getWarehouseView,
  getCategoryView,
  getPhysicalIntro,
} from "@/api/admin/home.js";
import { getOverviewList, getRecordList } from "@/api/admin/godown";
const router = useRouter();
const overviewList = ref([
  {
    label: "资料总数",
    value: "",
    unit: "本",
  },
  {
    label: "资料种类",
    value: "",
    unit: "种",
  },
  {
    label: "派送总数",
    value: "",
    unit: "本",
  },
  {
    label: "借阅申请",
    path: "physical/borrow/borrowApply",
    value: "",
    unit: "本",
  },
]);
const tableData = reactive({
  data1: [],
  data2: [],
  data3: [],
});
const throughoutData = ref({
  columns: [],
  rows: [],
});
const accrueData = ref({
  columns: [],
  rows: [],
});
const inforData = ref({
  columns: [],
  rows: [],
});
const handlePath = (path) => {
  if (!path) return;
  router.push(path);
};
const collectData = ref({
  cateNum: "",
  materialNum: "",
});
const statusData = ref({
  columns: [],
  rows: [],
});
const picLibView = ref({
  materialNum: "",
  clickStats: [],
  subStats: [],
});
const videoLibView = ref({
  materialNum: "",
  clickStats: [],
  subStats: [],
});
const warehouseView = ref({
  warehouseCount: 0, //仓库总数
  realStockTotal: 0, //库存合计
  records: [],
});
function getHomeData() {
  //首页统计-电子馆概览
  getElecLibView().then((res) => {
    collectData.value = res.data;
  });
  //首页统计-借阅状态统计
  getSubsStatusStats().then((res) => {
    // res.data.rows = res.data.rows.reverse();
    statusData.value = res.data;
  });
  //首页统计-各处统计
  getDeptSubsStats().then((res) => {
    throughoutData.value = res.data;
  });
  //首页统计-资料统计
  getMaterialStats().then((res) => {
    inforData.value = res.data;
  });
  //首页统计-图片馆概览
  // getPicLibView().then((res) => {
  //   picLibView.value = res.data;
  // });
  // //首页统计-视频馆概览
  // getVideoLibView().then((res) => {
  //   videoLibView.value = res.data;
  // });
  //首页统计-仓库统计
  getWarehouseView().then((res) => {
    warehouseView.value = res.data;
  });
  //首页统计-借阅累积
  getCategoryView().then((res) => {
    accrueData.value = res.data;
  });
  //首页统计-实体资料馆概览
  getPhysicalIntro().then((res) => {
    console.log(res.data);
    let { borrowApplyCount, materialNum, dispatchStock, materialStock } =
      res.data;
    overviewList.value[0].value = materialStock;
    overviewList.value[1].value = materialNum;
    overviewList.value[2].value = dispatchStock;
    overviewList.value[3].value = borrowApplyCount;
    console.log(overviewList);
  });
  //首页-库存概览
  getOverviewList({
    current: 1,
    size: 3,
  }).then((res) => {
    tableData.data1 = res.data.records || [];
  });
  //首页-近期派送出库
  getRecordList({
    current: 1,
    size: 3,
    recordTypeList: [2],
  }).then((res) => {
    tableData.data3 = res.data.records || [];
  });
  //首页-近期库存记录
  getRecordList({
    current: 1,
    size: 3,
    recordTypeList: [3],
  }).then((res) => {
    tableData.data2 = res.data.records || [];
  });
}
onMounted(() => {
  getHomeData();
});
</script>

<style scoped lang="scss">
.admin-home {
  .admin-home-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .stats-line0 {
    width: 100%;
    word-break: break-all;
    color: #db3124;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .stats-line1 {
    color: #e99d42;
  }
  .stats-line2 {
    color: #81b337;
  }
  .home-row-one {
    .home-border-line {
      height: 250px;
    }
  }
  .home-border-line {
    border: 1px #ddd solid;
    padding: 15px;
    margin-bottom: 20px;
    .descriptions-title {
      font-size: 20px;
      opacity: 0.7;
      font-weight: 600;
      margin-bottom: 20px;
    }
    .descriptions-num {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30px;
      span {
        font-size: 14px;
      }
    }
    .home-desc {
      margin-top: 40px;
      h5 {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        padding: 0;
      }
      ul,
      li {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      ul {
        li {
          font-size: 14px;
          display: flex;
          align-items: center;
          flex: 1;
          width: 100%;
          margin: 20px 0;
          justify-content: space-between;
          span {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-left: 20px;
            white-space: nowrap;
          }
        }
      }
    }
    :deep(.el-descriptions) {
      margin-bottom: 20px;

      .el-descriptions__cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .el-descriptions__label {
          flex: 1;
          width: 100%;
        }
        .el-descriptions__content {
          margin-left: 10px;
        }
      }
    }

    h2 {
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 20px 0;
      padding: 0;
      color: var(--el-menu-active-color);
      cursor: pointer;
    }

    .overview-item {
      margin: 10px 0;
    }

    .overview-current {
      color: var(--el-menu-active-color);
      cursor: pointer;

      span {
        color: #db3124;
      }
    }

    .overview-table {
      margin: 20px 0;

      .overview-table-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        span {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .collect-stat {
      color: var(--el-menu-active-color);
    }
  }

  .home-statistics {
    .home-statistics-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      span {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-menu-active-color);
      }
    }

    .home-statistics-chart {
      margin-bottom: 20px;
      .statistics-chart-line {
        margin: 20px;
        span {
          margin: 10px 0;
          display: flex;
          font-size: 16px;
          font-weight: bold;
          align-items: center;
        }
      }
    }
  }
}
</style>
