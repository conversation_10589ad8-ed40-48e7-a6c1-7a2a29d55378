<template>
  <div class="carousel-warp">
    <div
      v-for="(item, index) in carouselList"
      :key="index"
      class="carousel-item"
    >
      <div class="carousel-left">
        <div class="carousel-upload">
          <imageUpload v-model="item.comFileList" :limit="1" :fileSize="10" />
        </div>
        <div class="carousel-input">
          <el-form-item label="轮播页主题:" prop="title">
            <el-input
              v-model="item.title"
              placeholder="请输入轮播页主题"
              clearable
            />
          </el-form-item>
          <el-form-item label="轮播页链接:" prop="webUrl">
            <el-input
              v-model="item.webUrl"
              placeholder="请输入轮播页链接"
              clearable
            />
          </el-form-item>
        </div>
      </div>
      <div class="carousel-right">
        <el-button
          v-if="index === 0"
          type="primary"
          icon="Plus"
          circle
          @click="handleAdd"
        ></el-button>
        <el-button
          v-else
          type="primary"
          icon="Minus"
          circle
          @click="handleDelete(index)"
        ></el-button>
      </div>
    </div>
  </div>
</template>
<script setup name="Home">
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";

const emit = defineEmits();
const props = defineProps({
  data: {
    type: Array,
  },
});
const carouselItemAtom = {
  title: "",
  comFile: {},
  comFileList: [],
  webUrl: "",
};
const carouselList = ref([]);

const handleAdd = () => {
  if (carouselList.value.length <= 5)
    carouselList.value.push({ ...carouselItemAtom });
  else
    ElMessage({
      message: "已超过最多添加数量",
      type: "warning",
      duration: 3 * 1000,
    });
};
const handleDelete = (index) => {
  carouselList.value.splice(index, 1);
};
watch(
  carouselList,
  () => {
    console.log(carouselList.value);
    emit("value-changed", carouselList.value);
  },
  { deep: true, immediate: true }
);
watchEffect(() => {
  if (props.data && props.data.length > 0) {
    let params = deepClone(props.data);
    params.forEach((element) => {
      if (!element.comFileList || element.comFileList.length < 1)
        element.comFileList = [element.comFile];
    });
    carouselList.value = params;
  } else {
    carouselList.value = [{ ...carouselItemAtom }];
  }
});
</script>
<style lang="scss" scoped>
.carousel-warp {
  .carousel-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    .carousel-left {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      flex: 1 0 0;
      .carousel-upload {
        width: 240px;
        margin-right: 20px;
      }
      .carousel-input {
        flex: 1 1 auto;
      }
    }
    .carousel-right {
      width: 70px;
      display: flex;
      flex-direction: row;
      justify-content: center;
    }
  }
}
</style>
