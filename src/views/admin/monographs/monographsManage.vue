<template>
  <div class="home-container">
    <div class="catemana-warp">
      <div class="catemana-table">
        <div class="catemana-handle">
          <el-tree-select
            v-model="queryParams.subjectCategoryId"
            :data="categoryOptions"
            :props="{ value: 'id', label: 'subjectName', children: 'children' }"
            value-key="id"
            placeholder="请选择所属专题"
            check-strictly
            style="width: 300px; margin-right: 20px"
          />
          <el-button
            type="primary"
            class="handle-item"
            @click="handleOpen(1, null)"
            >录入资料</el-button
          >
          <el-input
            class="handle-item"
            placeholder="请输入专题资料名称"
            v-model="queryParams.subjectMaterialName"
            style="width: 400px"
          />
          <el-button type="primary" @click="handleQuery">筛选</el-button>
          <el-button type="primary" @click="handleReset">重置</el-button>
        </div>
        <div>
          <el-table :data="bookList" v-loading="loading">
            <el-table-column label="序号" align="center" prop="id">
              <template #default="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="专题资料名称"
              align="center"
              prop="subjectMaterialName"
            />
            <el-table-column
              label="资料所属专题"
              align="center"
              prop="subjectId"
            >
              <template #default="scope">
                {{ monographsName(scope.row.subjectId) }}
              </template>
            </el-table-column>

            <el-table-column
              label="录入时间"
              align="center"
              prop="createTime"
            />
            <el-table-column label="操作" align="center" width="150">
              <template #default="scope">
                <el-button type="primary" @click="handleOpen(2, scope.row)" link
                  >查看</el-button
                >
                <el-button type="primary" @click="handleOpen(0, scope.row)" link
                  >编辑</el-button
                >
                <el-button type="danger" link @click="deleteBook(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <el-dialog v-if="open" v-model="open" width="600px">
      <template #header>{{ title }}</template>
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        :disabled="status === 2"
        class="catemana-dialog"
      >
        <div class="catemana-dialog-left">
          <el-form-item label="专题资料名称" prop="subjectMaterialName">
            <el-input
              v-model="form.subjectMaterialName"
              placeholder="请输入专题资料名称"
            />
          </el-form-item>
          <el-form-item label="资料所属专题" prop="subjectId">
            <el-tree-select
              v-model="form.subjectId"
              :data="categoryOptions"
              :props="{
                value: 'id',
                label: 'subjectName',
                children: 'children',
              }"
              value-key="id"
              placeholder="请选择所属专题"
              check-strictly
            />
          </el-form-item>
          <el-form-item label="专题资料内容" prop="comFileList">
            <fileUpload
              v-if="status !== 2"
              v-model="form.comFileList"
              :limit="1"
              :fileType="['doc', 'docx', 'pdf']"
            />
            <div v-else>
              <el-button type="primary" link>{{
                form?.comFile?.fileName
              }}</el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addBook" v-if="status !== 2"
            >确 定</el-button
          >
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="monographsManage">
import {
  getMonographsList,
  getMonographsBookList,
  addMonographsBook,
  updateMonographsBook,
  deleteMonographsBook,
} from "@/api/admin/monographs.js";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";

const { proxy } = getCurrentInstance();
const route = useRoute();
const subjectMaterialType = computed(() => {
  // 1-电子资料 2-实体图书
  return route.query.pageType == "physical" ? 2 : 1;
});

const categoryOptions = ref(undefined);
const status = ref(null);
const bookList = ref([]);
const total = ref(0);
const open = ref(false);
const title = ref("");
const data = reactive({
  form: {},
  loading: false,
  queryParams: {
    current: 1,
    size: 10,
    subjectMaterialType: subjectMaterialType.value,
    subjectCategoryId: undefined,
    subjectMaterialName: undefined,
  },
  rules: {
    subjectMaterialName: [
      { required: true, message: "专题资料名称不能为空", trigger: "blur" },
    ],
    subjectId: [
      { required: true, message: "专题资料介绍不能为空", trigger: "blur" },
    ],
    comFileList: [
      { required: true, message: "专题资料文件不能为空", trigger: "blur" },
    ],
  },
});
const { queryParams, form, rules, loading } = toRefs(data);

/** 查询部门下拉树结构 */
function getTree() {
  getMonographsList({ subjectType: subjectMaterialType.value }).then(
    (response) => {
      categoryOptions.value = response.data;
    }
  );
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}
function handleReset() {
  queryParams.value = {
    current: 1,
    size: 10,
    subjectMaterialType: subjectMaterialType.value,
    subjectCategoryId: undefined,
    subjectMaterialName: undefined,
  };
  handleQuery();
}
/** 查询用户列表 */
function getList() {
  loading.value = true;
  getMonographsBookList(queryParams.value)
    .then((res) => {
      bookList.value = res.data.records;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
function monographsName(id) {
  let data = categoryOptions.value.filter((item) => {
    if (id === item.id) return true;
  });
  return data[0]?.subjectName;
}

function handleOpen(flag, item) {
  if (flag === 0) {
    status.value = flag;
    title.value = "编辑专题资料";
    form.value = {
      ...item,
      subjectMaterialType: subjectMaterialType.value,
      comFileList: [item.comFileList],
    };
  } else if (flag === 1) {
    status.value = flag;
    title.value = "录入专题资料";
    form.value = {
      subjectMaterialType: subjectMaterialType.value,
    };
  } else if (flag === 2) {
    status.value = flag;
    title.value = "查看专题资料";
    form.value = {
      ...item,
      subjectMaterialType: subjectMaterialType.value,
      comFileList: [item.comFileList],
    };
  }
  open.value = true;
}
function addBook() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const api = status.value === 1 ? addMonographsBook : updateMonographsBook;
      let params = deepClone(form.value);
      if (params.comFileList && params.comFileList.length > 0)
        params.comFile = {
          fileName: params.comFileList[0].name,
          fileUrl: params.comFileList[0].url,
          ossId: params.comFileList[0].ossId,
        };
      api(params)
        .then(() => {
          getList();
          ElMessage({
            message: "保存成功",
            type: "success",
            duration: 3 * 1000,
          });
          form.value = {};
          open.value = false;
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    }
  });
}
function deleteBook(row) {
  proxy.$modal
    .confirm('是否确认删除为"' + row.subjectMaterialName + '"的数据项?')
    .then(() => {
      proxy.$modal.loading();
      return deleteMonographsBook(row.id);
    })
    .then(() => {
      getList();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
getTree();
getList();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;

  .catemana-warp {
    display: flex;
    flex-direction: row;

    .catemana-sideline {
      width: 300px;
      min-height: calc(100vh - 230px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .catemana-line {
      border-right: 1px solid black;
      margin: 0 20px;
      min-height: calc(100vh - 230px);
    }

    .catemana-table {
      flex: 1 1 auto;
      min-height: calc(100vh - 230px);

      .catemana-handle {
        display: flex;
        padding: 10px 0 20px 0;

        .handle-item {
          margin-right: 20px;
        }
      }
    }
  }

  .catemana-dialog {
    display: flex;
    flex-direction: row;
    padding: 0 20px;

    .catemana-dialog-left {
    }
  }
}
</style>
