<template>
  <div class="home-container">
    <CarouselCard title="轮播图设置（轮播最多五张）：">
      <CarouselUpload
        :data="carouselList"
        @value-changed="handleChange"
      ></CarouselUpload>
    </CarouselCard>

    <div class="home-btn">
      <el-button type="primary" @click="addCarousel">保存</el-button>
    </div>
  </div>
  <CarouselCard title="专题设置：">
    <MonographsUpload></MonographsUpload>
  </CarouselCard>
</template>
<script setup name="MonographsSetting">
import { addHomeCarousel, getHomeCarousel } from "@/api/admin/home.js";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";
import CarouselUpload from "@/views/admin/home/<USER>/CarouselUpload.vue";
import CarouselCard from "@/views/admin/home/<USER>/CarouselCard.vue";
import MonographsUpload from "./components/MonographsUpload.vue";

const { proxy } = getCurrentInstance();
const route = useRoute();
const subjectType = computed(() => {
  // electronic_subject 电子资料馆专题轮播图, physical_subject 实体资料馆专题轮播图
  return route.query.pageType == "physical" ? "physical_subject" : "electronic_subject"
})

const carouselList = ref([]);
const carouselListNew = ref([]);
function addCarousel() {
  proxy.$modal.loading();
  let params = deepClone(carouselListNew.value);
  params.forEach((item) => {
    if (item.comFileList && item.comFileList.length > 0)
      item.comFile = item.comFileList[0];
  });
  addHomeCarousel({ sysRollList: params, subjectType: subjectType.value })
    .then((res) => {
      getCarousel();
      ElMessage({
        message: "保存成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function getCarousel() {
  proxy.$modal.loading();
  getHomeCarousel({ subjectType: subjectType.value })
    .then((res) => {
      carouselList.value = res.data;
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function handleChange(itemList) {
  carouselListNew.value = itemList;
}
getCarousel();
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  .input-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .el-form-item {
      width: 49%;
    }
  }
  .home-btn {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
