<template>
  <div class="monographs-warp">
    <div class="monographs-tips">
      <div>设置说明：当前类目下有资料时，无法删除类目</div>
      <div class="monographs-addbtn">
        <el-button type="primary" icon="Plus" circle @click="handleAddMonographs(null, true)"></el-button>
      </div>
    </div>
    <div class="monographs-list">
      <div class="monographs-item" v-for="(item, index) in monographsList" :key="index">
        <div class="monographs-title">
          <el-form-item label="专题名称：" label-width="100">
            <el-input v-model="item.subjectName" disabled />
          </el-form-item>
          <div class="monographs-btn">
            <el-button type="primary" icon="EditPen" style="margin-left: 10px; margin-right: 10px; width: 40%;"
              @click="handleAddMonographs(item, false)">
              编辑专题
            </el-button>
            <el-button type="primary" icon="Delete" @click="deleteMonographs(item.id)" style=" width: 40%;">删除专题</el-button>
          </div>
        </div>
        <div class="monographs-title">
          <el-form-item label="专题优先级：" label-width="100">
            <el-input v-model="item.orderNum" disabled />
          </el-form-item>
        </div>
        <div class="monographs-details">
          <el-form-item label="专题介绍：" class="monographs-intro" label-width="100px">
            <el-input v-model="item.subjectIntro" disabled type="textarea" :autosize="{ minRows: 9, maxRows: 9 }"
              placeholder="请输入内容" />
          </el-form-item>
          <div class="monographs-img">
            <ImagePreview :width="200" :height="200" :src="`${urlPrefix}${item?.comFile?.fileUrl}`" />
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-if="open" v-model="open" width="600">
      <template #header>{{ title }}</template>
      <el-form ref="addForm" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="专题名称：" prop="subjectName">
          <el-input v-model="form.subjectName" />
        </el-form-item>
        <el-form-item label="专题优先级：" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="专题介绍：" prop="subjectIntro">
          <el-input v-model="form.subjectIntro" type="textarea" :autosize="{ minRows: 2, maxRows: 9 }"
            placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="专题封面：" prop="comFileList">
          <imageUpload v-model="form.comFileList" :limit="1" :fileSize="10" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addMonographs">确 定</el-button>
          <el-button @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="MonographsUpload">
import {
  getMonographsList,
  addMonographsList,
  updateMonographsList,
  deleteMonographsList,
} from "@/api/admin/monographs.js";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/index";

const { proxy } = getCurrentInstance();
const route = useRoute();
const subjectType = computed(() => {
  // 1 电子资料馆, 2 实体资料馆
  return route.query.pageType == "physical" ? 2 : 1
})

const open = ref(false);
const form = ref({
  subjectType: subjectType.value,
});
const loading = ref(false)
const urlPrefix = `${window.location.protocol}//${window.location.host}`;
const title = ref("添加专题");
const monographsList = ref([]);
const rules = ref({
  subjectName: [
    { required: true, message: "专题名称不能为空", trigger: "blur" },
  ],
  subjectIntro: [
    { required: true, message: "专题介绍不能为空", trigger: "blur" },
  ],
  comFileList: [
    { required: true, message: "专题封面不能为空", trigger: "blur" },
  ],
});
// type == true时新增 ==false为修改
function handleAddMonographs(item, flag) {
  if (flag) {
    // add
    title.value = "新增专题"
    form.value = {
      subjectType: subjectType.value,
    };
  } else {
    // edit
    title.value = "编辑专题"
    form.value = {
      ...item,
      comFileList: [item.comFile],
      subjectType: subjectType.value,
    };
  }
  open.value = true;
}
function getList() {
  proxy.$modal.loading();
  getMonographsList({ subjectType: subjectType.value })
    .then((res) => {
      monographsList.value = res.data;
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
function addMonographs() {
  proxy.$refs["addForm"].validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      loading.value = true;
      let params = deepClone(form.value);
      console.log(params.comFileList && params.comFileList.length > 0);
      if (params.comFileList && params.comFileList.length > 0)
        params.comFile = params.comFileList[0];
      const api = params.id ? updateMonographsList : addMonographsList;
      api(params)
        .then((res) => {
          getList();
          open.value = false;
          ElMessage({
            message: `${params.id ? '编辑' : '新增'}成功`,
            type: "success",
            duration: 3 * 1000,
          });
        })
        .finally(() => {
          loading.value = false;
          proxy.$modal.closeLoading();

        });
    }
  });
}
function deleteMonographs(index) {
  proxy.$modal
    .confirm("是否确认删除该专题?")
    .then(() => {
      proxy.$modal.loading();
      return deleteMonographsList(index);
    })
    .then(() => {
      getList();
      ElMessage({
        message: "删除成功",
        type: "success",
        duration: 3 * 1000,
      });
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}
getList();
</script>
<style lang="scss" scoped>
.monographs-warp {
  width: 100%;

  .monographs-tips {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;

    .monographs-addbtn {
      margin-left: 20px;
    }
  }

  .monographs-list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;

    .monographs-item {
      border: 2px solid rgb(205, 205, 205);
      padding: 25px;
      border-radius: 10px;
      width: 47%;
      margin-right: 2%;
      margin-bottom: 20px;

      &:hover {
        background-color: rgb(242, 242, 242);
      }

      :deep(.el-form-item) {
        margin-bottom: 0px;
      }

      .monographs-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;

        .monographs-btn {
          margin-left: 20px;
        }
      }

      .monographs-details {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        overflow: hidden;

        .monographs-intro {
          width: calc(100% - 230px);
        }

        .monographs-img {
          padding: 5px;
        }
      }
    }
  }
}
</style>
