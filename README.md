## 平台简介

* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。

## 前端运行

```bash
# 进入项目目录
cd ruoyi-ui-vue3

# 安装依赖
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```


## 相关文档

-  element-plus 组件库

https://element-plus.org/zh-CN/component/overview.html


## 接口文档

yapi地址：http://*************:40001/group/298

## 原型地址

https://modao.cc/app/chshy2gosgdxcejUDNnrr


## 问题

1. 年鉴类资料直接打开，不需要kkfile
2. 实体资料类型使用的电子资料的。

